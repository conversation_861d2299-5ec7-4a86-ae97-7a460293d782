<?php
/**
 * Email Notification System
 * 
 * This file handles sending email notifications to users.
 */

require_once 'config.php';

/**
 * Send email notification to user
 * 
 * @param int $userId User ID
 * @param string $type Notification type
 * @param string $subject Email subject
 * @param string $message Email message
 * @return bool Success status
 */
function sendEmailNotification($userId, $type, $subject, $message) {
    try {
        $db = getDbConnection();
        
        // Get user email and notification preferences
        $stmt = $db->prepare("
            SELECT u.email, us.email_notifications, us.id_card_updates, us.document_reminders, 
                   us.account_activity, us.news_updates
            FROM users u
            LEFT JOIN user_settings us ON u.user_id = us.user_id
            WHERE u.user_id = ?
        ");
        $stmt->execute([$userId]);
        $user = $stmt->fetch();
        
        if (!$user || !$user['email']) {
            return false;
        }
        
        // Check if email notifications are enabled
        if (!$user['email_notifications']) {
            return false;
        }
        
        // Check if specific notification type is enabled
        $typeEnabled = false;
        switch ($type) {
            case 'id_card':
                $typeEnabled = $user['id_card_updates'];
                break;
            case 'document':
                $typeEnabled = $user['document_reminders'];
                break;
            case 'account':
                $typeEnabled = $user['account_activity'];
                break;
            case 'news':
                $typeEnabled = $user['news_updates'];
                break;
            default:
                $typeEnabled = true; // For system notifications
        }
        
        if (!$typeEnabled) {
            return false;
        }
        
        // Validate Gmail address
        if (!str_ends_with($user['email'], '@gmail.com')) {
            error_log("Email notification failed: Invalid email address for user $userId");
            return false;
        }
        
        // Send email using PHP mail function
        $headers = [
            'From: Student Portal <<EMAIL>>',
            'Reply-To: <EMAIL>',
            'Content-Type: text/html; charset=UTF-8',
            'X-Mailer: PHP/' . phpversion()
        ];
        
        $htmlMessage = "
        <html>
        <head>
            <title>$subject</title>
        </head>
        <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
            <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                <div style='background-color: #5e259b; color: white; padding: 20px; text-align: center;'>
                    <h1 style='margin: 0;'>Student Portal</h1>
                </div>
                <div style='padding: 20px; background-color: #f9f9f9;'>
                    <h2 style='color: #5e259b;'>$subject</h2>
                    <p>$message</p>
                    <hr style='border: 1px solid #ddd; margin: 20px 0;'>
                    <p style='font-size: 12px; color: #666;'>
                        This is an automated message from Student Portal. Please do not reply to this email.
                        <br>
                        If you no longer wish to receive these notifications, you can update your preferences in your account settings.
                    </p>
                </div>
            </div>
        </body>
        </html>
        ";
        
        $success = mail($user['email'], $subject, $htmlMessage, implode("\r\n", $headers));
        
        if ($success) {
            // Log the email notification
            logActivity($userId, 'EMAIL_SENT', "Email notification sent: $type - $subject");
        } else {
            error_log("Failed to send email notification to user $userId");
        }
        
        return $success;
        
    } catch (Exception $e) {
        error_log("Email notification error: " . $e->getMessage());
        return false;
    }
}

/**
 * Send ID card status update notification
 */
function sendIdCardNotification($userId, $status) {
    $subjects = [
        'PENDING' => 'ID Card Application Received',
        'APPROVED' => 'ID Card Application Approved',
        'REJECTED' => 'ID Card Application Rejected',
        'COMPLETED' => 'ID Card Ready for Pickup'
    ];
    
    $messages = [
        'PENDING' => 'Your ID card application has been received and is currently being reviewed. We will notify you once the review is complete.',
        'APPROVED' => 'Great news! Your ID card application has been approved. Your ID card is now being processed.',
        'REJECTED' => 'Unfortunately, your ID card application has been rejected. Please check your application details and resubmit if necessary.',
        'COMPLETED' => 'Your ID card is ready for pickup! Please visit the student services office with a valid photo ID to collect your card.'
    ];
    
    $subject = $subjects[$status] ?? 'ID Card Application Update';
    $message = $messages[$status] ?? 'Your ID card application status has been updated.';
    
    return sendEmailNotification($userId, 'id_card', $subject, $message);
}

/**
 * Send document reminder notification
 */
function sendDocumentReminder($userId, $documentType) {
    $subject = 'Document Submission Reminder';
    $message = "This is a reminder that you have pending document submissions for your student ID application. Please upload your $documentType as soon as possible to avoid delays in processing.";
    
    return sendEmailNotification($userId, 'document', $subject, $message);
}

/**
 * Send account activity notification
 */
function sendAccountActivityNotification($userId, $activity) {
    $subject = 'Account Activity Notification';
    $message = "There has been recent activity on your Student Portal account: $activity. If this was not you, please contact support immediately.";
    
    return sendEmailNotification($userId, 'account', $subject, $message);
}

/**
 * Send news and updates notification
 */
function sendNewsNotification($userId, $title, $content) {
    $subject = "Student Portal News: $title";
    $message = $content;
    
    return sendEmailNotification($userId, 'news', $subject, $message);
}

/**
 * Send login notification
 */
function sendLoginNotification($userId) {
    $subject = 'Login Notification';
    $message = 'You have successfully logged into your Student Portal account at ' . date('Y-m-d H:i:s') . '. If this was not you, please contact support immediately.';
    
    return sendEmailNotification($userId, 'account', $subject, $message);
}
?>
