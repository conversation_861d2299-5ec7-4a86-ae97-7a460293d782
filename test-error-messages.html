<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error Message Display Test - Student ID Application</title>
    <link rel="stylesheet" href="css/createid.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            background: #5e259b;
            color: white;
        }
        .test-button:hover {
            background: #4a1d7a;
        }
        .mock-face-section {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .mock-face-preview {
            width: 378px;
            height: 227px;
            background: #ddd;
            border-radius: 10px;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Facial Verification Error Message Display Test</h1>
        <p>This page tests the error message positioning and styling for the facial verification system.</p>

        <div class="test-section">
            <h3>Mock Face Capture Section</h3>
            <div class="mock-face-section">
                <h4>Step 2: Capture Face</h4>
                <div class="mock-face-preview">
                    Mock Face Photo Preview
                </div>
                
                <!-- Verification Section -->
                <div class="form-group verification-section">
                    <div class="verification-container">
                        <button type="button" class="verify-btn capture-btn" id="testVerifyBtn">
                            <i class="fas fa-shield-check"></i> Verify Identity
                        </button>
                        <div class="verification-status" id="testVerificationStatus" style="display: none;">
                            <div class="status-icon"></div>
                            <div class="status-message"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>Error Message Tests</h3>
            <p>Click the buttons below to test different error message scenarios:</p>
            
            <button class="test-button" onclick="showSuccessMessage()">
                <i class="fas fa-check"></i> Test Success Message
            </button>
            
            <button class="test-button" onclick="showErrorMessage()">
                <i class="fas fa-times"></i> Test Error Message
            </button>
            
            <button class="test-button" onclick="showNoFaceError()">
                <i class="fas fa-exclamation-triangle"></i> Test No Face Detected
            </button>
            
            <button class="test-button" onclick="showLowConfidenceError()">
                <i class="fas fa-question"></i> Test Low Confidence
            </button>
            
            <button class="test-button" onclick="showPopupError()">
                <i class="fas fa-bell"></i> Test Popup Error
            </button>
            
            <button class="test-button" onclick="clearMessages()">
                <i class="fas fa-eraser"></i> Clear All Messages
            </button>
        </div>

        <div class="test-section">
            <h3>Next Form Section (Emergency Contact)</h3>
            <div class="form-group">
                <label for="testEmergencyContact">Emergency Contact:</label>
                <input type="text" id="testEmergencyContact" placeholder="This should not be affected by error messages above">
            </div>
        </div>
    </div>

    <script>
        function showVerificationStatus(type, message) {
            const statusElement = document.getElementById('testVerificationStatus');
            const messageElement = statusElement.querySelector('.status-message');
            const iconElement = statusElement.querySelector('.status-icon');

            messageElement.textContent = message;
            
            // Update icon based on type
            iconElement.innerHTML = type === 'success' 
                ? '<i class="fas fa-check-circle" style="color: #28a745;"></i>'
                : '<i class="fas fa-exclamation-triangle" style="color: #dc3545;"></i>';

            statusElement.style.display = 'block';
            statusElement.className = `verification-status ${type}`;
        }

        function showSuccessMessage() {
            showVerificationStatus('success', 'Identity verified successfully! (87% match)');
        }

        function showErrorMessage() {
            showVerificationStatus('error', 'Facial verification failed. Please ensure your face is clearly visible and matches your ID document, then try again. (43% match)');
        }

        function showNoFaceError() {
            showVerificationStatus('error', 'No face detected in face photo. Please ensure the image shows a clear, front-facing face.');
        }

        function showLowConfidenceError() {
            showVerificationStatus('error', 'Face detection confidence too low in ID document. Please capture a clearer image.');
        }

        function showPopupError() {
            // Create popup error like the actual system
            const errorDiv = document.createElement('div');
            errorDiv.className = 'verification-error-message';
            errorDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background-color: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
                border-radius: 6px;
                padding: 8px 12px;
                max-width: 350px;
                z-index: 1000;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                font-size: 8px;
                line-height: 1.3;
                font-weight: 500;
            `;
            errorDiv.innerHTML = `
                <div style="display: flex; align-items: flex-start; gap: 6px;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 10px; margin-top: 1px; flex-shrink: 0;"></i>
                    <span style="flex: 1;">Facial recognition models are still loading. Please wait...</span>
                    <button onclick="this.parentElement.parentElement.remove()" style="margin-left: auto; background: none; border: none; font-size: 12px; cursor: pointer; padding: 0; line-height: 1;">&times;</button>
                </div>
            `;
            
            document.body.appendChild(errorDiv);
            
            // Auto-remove after 6 seconds
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.remove();
                }
            }, 6000);
        }

        function clearMessages() {
            const statusElement = document.getElementById('testVerificationStatus');
            statusElement.style.display = 'none';
            
            // Remove any popup messages
            const popupMessages = document.querySelectorAll('.verification-error-message');
            popupMessages.forEach(msg => msg.remove());
        }

        // Test responsive behavior
        function testResponsive() {
            console.log('Testing responsive behavior...');
            console.log('Current window width:', window.innerWidth);
            
            const statusElement = document.getElementById('testVerificationStatus');
            const computedStyle = window.getComputedStyle(statusElement);
            console.log('Status element font-size:', computedStyle.fontSize);
            console.log('Status element padding:', computedStyle.padding);
        }

        // Run responsive test on window resize
        window.addEventListener('resize', testResponsive);
        
        // Initial test
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Error message display test page loaded');
            testResponsive();
        });
    </script>
</body>
</html>
