<?php
/**
 * Profile Page
 *
 * This is the profile page for authenticated users.
 */

require_once '../backend/config.php';
require_once '../backend/auth.php';

// Redirect to login if not authenticated
if (!isLoggedIn()) {
    header('Location: ../index.php');
    exit;
}

// Get user data from session
$userId = $_SESSION['user_id'];
$username = $_SESSION['username'];
$fullName = $_SESSION['full_name'];
$studentId = $_SESSION['student_id'];

// Get user profile data
try {
    $db = getDbConnection();

    // Get user profile
    $stmt = $db->prepare("
        SELECT * FROM user_profiles
        WHERE user_id = ?
    ");
    $stmt->execute([$userId]);
    $profile = $stmt->fetch();

} catch (PDOException $e) {
    error_log("Profile Error: " . $e->getMessage());
    $profile = null;
}

// Get user initials for avatar
$initials = '';
if ($fullName) {
    $nameParts = explode(' ', $fullName);
    if (count($nameParts) >= 2) {
        $initials = substr($nameParts[0], 0, 1) . substr($nameParts[1], 0, 1);
    } else {
        $initials = substr($fullName, 0, 2);
    }
} else {
    $initials = substr($username, 0, 2);
}
$initials = strtoupper($initials);

// Generate CSRF token if it doesn't exist
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile - Student Portal</title>
    <link rel="stylesheet" href="../css/dashboard.css">
    <link rel="stylesheet" href="../css/profile.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="../js/theme-loader.js"></script>
    <?php include 'theme-sync.php'; ?>
</head>
<body>
    <div class="dashboard-container">
        <?php
        // Set current page for sidebar highlighting
        $currentPage = 'profile';

        // Include sidebar template
        include 'sidebar-template.php';
        ?>

        <!-- Main Content -->
        <div class="main-content">
            <div class="top-bar">
                <button type="button" class="menu-toggle" title="Toggle Menu">
                    <i class="fas fa-bars"></i>
                </button>

                <div class="top-icons">
                    <div class="icon-badge">
                        <i class="fas fa-bell"></i>
                        <span class="badge">1</span>
                    </div>
                    <div class="icon-badge">
                        <i class="fas fa-envelope"></i>
                        <span class="badge">2</span>
                    </div>
                </div>
            </div>

            <div class="dashboard-content">
                <div class="profile-header">
                    <h1>Profile</h1>
                    <p>Manage your personal information</p>

                    <?php if (isset($_SESSION['profile_message'])): ?>
                        <div class="alert alert-success">
                            <?php
                                echo htmlspecialchars($_SESSION['profile_message']);
                                unset($_SESSION['profile_message']);
                            ?>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($_SESSION['profile_error'])): ?>
                        <div class="alert alert-error">
                            <?php
                                echo htmlspecialchars($_SESSION['profile_error']);
                                unset($_SESSION['profile_error']);
                            ?>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="profile-content">
                    <div class="profile-section">
                        <div class="section-header">
                            <h2>Personal Information</h2>
                            <p>Update your personal details</p>
                        </div>

                        <form id="personalInfoForm" action="../backend/update_profile.php" method="post" enctype="multipart/form-data">
                            <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">

                            <div class="form-group">
                                <label for="fullName">Full Name</label>
                                <input type="text" id="fullName" name="full_name" value="<?php echo htmlspecialchars($fullName); ?>">
                            </div>

                            <div class="form-group">
                                <label>Profile Image</label>
                                <div class="profile-image-container">
                                    <div class="profile-image">
                                        <?php if (isset($profile['profile_image']) && !empty($profile['profile_image'])): ?>
                                            <img src="<?php echo htmlspecialchars($profile['profile_image']); ?>" alt="Profile Image">
                                        <?php else: ?>
                                            <i class="fas fa-user"></i>
                                        <?php endif; ?>
                                    </div>
                                    <div class="upload-button">
                                        <label for="imageUpload" class="upload-label">
                                            <i class="fas fa-upload"></i> Upload Image
                                        </label>
                                        <input type="file" id="imageUpload" name="profile_image" accept="image/jpeg, image/png" hidden>
                                        <p class="file-info">JPG or PNG. Max size 2MB.</p>
                                    </div>
                                </div>
                            </div>

                            <button type="submit" class="save-button">Save Changes</button>
                        </form>
                    </div>
                </div>
            </div>

            <?php include 'footer-template.php'; ?>
        </div>
    </div>

    <script src="../js/dashboard.js"></script>
    <script src="../js/profile.js"></script>
</body>
</html>
