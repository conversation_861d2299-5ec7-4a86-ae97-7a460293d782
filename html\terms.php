<?php
/**
 * Terms of Service Page
 *
 * This page displays the Terms of Service for the Student ID Application.
 */

require_once '../backend/config.php';
require_once '../backend/auth.php';

// Redirect to login if not authenticated
if (!isLoggedIn()) {
    header('Location: ../index.php');
    exit;
}

// Get user data from session
$userId = $_SESSION['user_id'];
$username = $_SESSION['username'];
$fullName = $_SESSION['full_name'];
$studentId = $_SESSION['student_id'];

// Get user initials for avatar
$initials = '';
if ($fullName) {
    $nameParts = explode(' ', $fullName);
    if (count($nameParts) >= 2) {
        $initials = substr($nameParts[0], 0, 1) . substr($nameParts[1], 0, 1);
    } else {
        $initials = substr($fullName, 0, 2);
    }
} else {
    $initials = substr($username, 0, 2);
}
$initials = strtoupper($initials);

// Set the last updated date for Terms of Service
$lastUpdated = "May 20, 2024";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Terms of Service - Student Portal</title>
    <link rel="stylesheet" href="../css/dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="../js/theme-loader.js"></script>
    <?php include 'theme-sync.php'; ?>
    <?php include 'message-count.php'; ?>
</head>
<body>
    <div class="dashboard-container">
        <?php
        // Set current page for sidebar highlighting
        $currentPage = '';

        // Include sidebar template
        include 'sidebar-template.php';
        ?>

        <!-- Main Content -->
        <div class="main-content">
            <div class="top-bar">
                <button type="button" class="menu-toggle" title="Toggle Menu">
                    <i class="fas fa-bars"></i>
                </button>

                <div class="top-icons">
                    <div class="icon-badge">
                        <i class="fas fa-bell"></i>
                        <span class="badge">1</span>
                    </div>
                    <div class="icon-badge">
                        <i class="fas fa-envelope"></i>
                        <?php if ($messageCount > 0): ?>
                        <span class="badge"><?php echo $messageCount; ?></span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="dashboard-content">
                <div class="page-header">
                    <h1>Terms of Service</h1>
                    <p>Please read these terms carefully before using our services</p>
                </div>

                <div class="terms-container">
                    <p class="last-updated">Last Updated: <?php echo $lastUpdated; ?></p>

                    <p>Welcome to the Student ID Application portal. Please read these Terms of Service ("Terms") carefully before using our website and services.</p>

                    <h2>1. ACCEPTANCE OF TERMS</h2>
                    <p>By accessing or using the Student ID Application portal ("Service"), you agree to be bound by these Terms. If you disagree with any part of the Terms, you may not access the Service.</p>

                    <h2>2. DESCRIPTION OF SERVICE</h2>
                    <p>The Student ID Application portal is an online platform that allows students to:</p>
                    <ul>
                        <li>Apply for and manage student identification cards</li>
                        <li>Upload and store required documentation</li>
                        <li>Update personal information</li>
                        <li>Access student-related services</li>
                    </ul>

                    <h2>3. ELIGIBILITY</h2>
                    <p>To use the Service, you must be:</p>
                    <ul>
                        <li>A currently enrolled or admitted student at the University</li>
                        <li>At least 16 years of age</li>
                        <li>Able to form legally binding contracts</li>
                        <li>Not prohibited from using the Service under applicable laws</li>
                    </ul>

                    <h2>4. USER ACCOUNTS</h2>
                    <h3>4.1 Registration</h3>
                    <p>To access certain features of the Service, you must register for an account using your official student credentials.</p>

                    <h3>4.2 Account Security</h3>
                    <p>You are responsible for:</p>
                    <ul>
                        <li>Maintaining the confidentiality of your account information</li>
                        <li>All activities that occur under your account</li>
                        <li>Notifying us immediately of any unauthorized use of your account</li>
                    </ul>

                    <h3>4.3 Accurate Information</h3>
                    <p>You agree to provide accurate, current, and complete information during registration and to update such information to keep it accurate, current, and complete.</p>

                    <h2>5. PRIVACY POLICY</h2>
                    <p>Your use of the Service is also governed by our Privacy Policy, which is incorporated by reference into these Terms. Please review our Privacy Policy.</p>

                    <h2>6. USER CONTENT</h2>
                    <h3>6.1 Ownership</h3>
                    <p>You retain all rights to any content you submit, post, or display on or through the Service ("User Content").</p>

                    <h3>6.2 License</h3>
                    <p>By submitting User Content, you grant the University a worldwide, non-exclusive, royalty-free license to use, reproduce, process, and display your User Content solely for the purposes of providing and improving the Service.</p>

                    <h3>6.3 Responsibility</h3>
                    <p>You are solely responsible for your User Content and the consequences of posting or publishing it.</p>

                    <h2>7. PROHIBITED CONDUCT</h2>
                    <p>You agree not to:</p>
                    <ul>
                        <li>Use the Service for any illegal purpose</li>
                        <li>Violate any laws in your jurisdiction</li>
                        <li>Impersonate another person or misrepresent your affiliation</li>
                        <li>Attempt to gain unauthorized access to the Service</li>
                        <li>Interfere with or disrupt the Service</li>
                        <li>Upload viruses or other malicious code</li>
                        <li>Collect or harvest user data without permission</li>
                        <li>Use the Service to send unsolicited communications</li>
                    </ul>

                    <h2>8. INTELLECTUAL PROPERTY</h2>
                    <h3>8.1 Service Content</h3>
                    <p>The Service and its original content, features, and functionality are owned by the University and are protected by copyright, trademark, and other intellectual property laws.</p>

                    <h3>8.2 Restrictions</h3>
                    <p>You may not:</p>
                    <ul>
                        <li>Modify, adapt, or hack the Service</li>
                        <li>Create derivative works based on the Service</li>
                        <li>Use the Service for commercial purposes without authorization</li>
                    </ul>

                    <h2>9. TERMINATION</h2>
                    <h3>9.1 By User</h3>
                    <p>You may terminate your account at any time by contacting student services.</p>

                    <h3>9.2 By University</h3>
                    <p>We may terminate or suspend your account immediately, without prior notice or liability, for any reason, including if you breach the Terms.</p>

                    <h3>9.3 Effect of Termination</h3>
                    <p>Upon termination, your right to use the Service will immediately cease.</p>

                    <h2>10. DISCLAIMER OF WARRANTIES</h2>
                    <p>The Service is provided "as is" and "as available" without warranties of any kind, either express or implied, including, but not limited to, implied warranties of merchantability, fitness for a particular purpose, or non-infringement.</p>

                    <h2>11. LIMITATION OF LIABILITY</h2>
                    <p>In no event shall the University be liable for any indirect, incidental, special, consequential, or punitive damages, including loss of profits, data, or other intangible losses, resulting from your access to or use of or inability to access or use the Service.</p>

                    <h2>12. INDEMNIFICATION</h2>
                    <p>You agree to defend, indemnify, and hold harmless the University and its officers, directors, employees, and agents from any claims, liabilities, damages, losses, and expenses arising out of or in any way connected with your use of the Service.</p>

                    <h2>13. CHANGES TO TERMS</h2>
                    <p>We reserve the right to modify or replace these Terms at any time. If a revision is material, we will provide at least 30 days' notice prior to any new terms taking effect.</p>

                    <h2>14. GOVERNING LAW</h2>
                    <p>These Terms shall be governed by the laws of the state where the University is located, without regard to its conflict of law provisions.</p>

                    <h2>15. CONTACT INFORMATION</h2>
                    <p>If you have any questions about these Terms, please contact us at:</p>
                    <ul>
                        <li>Email: <EMAIL></li>
                        <li>Phone: (*************</li>
                        <li>Address: University Campus, Main Building</li>
                    </ul>

                    <h2>16. ACADEMIC INTEGRITY</h2>
                    <p>Users of this Service are expected to adhere to the University's academic integrity policies. Using this Service to facilitate academic dishonesty is strictly prohibited.</p>

                    <h2>17. DATA RETENTION</h2>
                    <p>Student ID information and associated documentation will be retained in accordance with the University's data retention policies and applicable laws.</p>

                    <a href="dashboard.php" class="return-button">Return to Dashboard</a>
                </div>
            </div>

            <?php include 'footer-template.php'; ?>
        </div>
    </div>

    <script src="../js/dashboard.js"></script>
</body>
</html>
