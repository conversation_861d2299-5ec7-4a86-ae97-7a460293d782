document.addEventListener('DOMContentLoaded', function() {
    // Image upload preview
    const imageUpload = document.getElementById('imageUpload');
    const profileImage = document.querySelector('.profile-image');

    imageUpload.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            // Validate file type
            const fileType = file.type;
            if (fileType !== 'image/jpeg' && fileType !== 'image/png') {
                alert('Please select a JPG or PNG image file.');
                this.value = ''; // Clear the file input
                return;
            }

            // Validate file size (max 2MB)
            const maxSize = 2 * 1024 * 1024; // 2MB in bytes
            if (file.size > maxSize) {
                alert('File size exceeds the limit of 2MB.');
                this.value = ''; // Clear the file input
                return;
            }

            const reader = new FileReader();

            reader.onload = function(e) {
                // Remove the icon
                profileImage.innerHTML = '';

                // Create and add the image
                const img = document.createElement('img');
                img.src = e.target.result;
                img.style.width = '100%';
                img.style.height = '100%';
                img.style.borderRadius = '50%';
                img.style.objectFit = 'cover';

                profileImage.appendChild(img);
            }

            reader.readAsDataURL(file);
        }
    });

    // Form submission
    const personalInfoForm = document.getElementById('personalInfoForm');

    personalInfoForm.addEventListener('submit', function(e) {
        // Check if a file is selected and validate it before submitting
        const fileInput = document.getElementById('imageUpload');
        if (fileInput.files.length > 0) {
            const file = fileInput.files[0];

            // Validate file type
            const fileType = file.type;
            if (fileType !== 'image/jpeg' && fileType !== 'image/png') {
                e.preventDefault();
                alert('Please select a JPG or PNG image file.');
                return false;
            }

            // Validate file size (max 2MB)
            const maxSize = 2 * 1024 * 1024; // 2MB in bytes
            if (file.size > maxSize) {
                e.preventDefault();
                alert('File size exceeds the limit of 2MB.');
                return false;
            }
        }

        // If validation passes, the form will submit normally
        return true;
    });
});