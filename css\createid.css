/* Create ID Page Styles */
.page-header {
    margin-bottom: 30px;
}

.page-header h1 {
    font-size: 24px;
    margin-bottom: 5px;
}

.page-header p {
    color: #666;
}

.id-card-container {
    display: flex;
    gap: 30px;
}

.id-card-form-section {
    flex: 1;
    background-color: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.id-card-preview-section {
    width: 350px;
    background-color: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.section-header {
    margin-bottom: 20px;
}

.section-header h2 {
    font-size: 18px;
    margin-bottom: 5px;
}

.section-header p {
    color: #666;
    font-size: 14px;
}

/* Form Styling */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
}

.form-group select {
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 15px center;
    background-size: 15px;
}

/* Photo Upload Styling */
.photo-upload-container {
    display: flex;
    align-items: center;
    gap: 20px;
}

.photo-preview {
    width: 120px;
    height: 150px;
    background-color: #f5f5f5;
    border: 1px dashed #ddd;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 40px;
}

.upload-button {
    flex: 1;
}

.upload-label {
    display: inline-block;
    background-color: #5e259b;
    color: white;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    margin-bottom: 10px;
}

.upload-label:hover {
    background-color: #4a1d7a;
}

.upload-label i {
    margin-right: 5px;
}

.file-info {
    font-size: 12px;
    color: #666;
}

/* Checkbox Styling */
.checkbox-group {
    display: flex;
    align-items: center;
}

.checkbox-group input[type="checkbox"] {
    margin-right: 10px;
}

/* User Type Selection */
.user-type-selection {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 10px;
    border: 2px solid #e9ecef;
}

.user-type-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.user-type-option {
    position: relative;
}

.user-type-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.user-type-label {
    display: flex;
    align-items: center;
    padding: 20px;
    background-color: white;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    height: 100%;
}

.user-type-label:hover {
    border-color: #5e259b;
    box-shadow: 0 2px 10px rgba(94, 37, 155, 0.1);
}

.user-type-option input[type="radio"]:checked + .user-type-label {
    border-color: #5e259b;
    background-color: #f8f4ff;
    box-shadow: 0 4px 15px rgba(94, 37, 155, 0.2);
}

.user-type-icon {
    margin-right: 15px;
    font-size: 24px;
    color: #5e259b;
    min-width: 40px;
    text-align: center;
}

.user-type-content h3 {
    margin: 0 0 5px 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.user-type-content p {
    margin: 0;
    color: #666;
    font-size: 14px;
    line-height: 1.4;
}

/* User Form Sections */
.user-form-section {
    margin-top: 20px;
}

/* Coming Soon Message */
.coming-soon-message {
    text-align: center;
    padding: 40px 20px;
    background-color: #f8f9fa;
    border-radius: 10px;
    border: 2px dashed #dee2e6;
}

.coming-soon-icon {
    font-size: 48px;
    color: #6c757d;
    margin-bottom: 20px;
}

.coming-soon-message h3 {
    color: #495057;
    margin-bottom: 10px;
    font-size: 24px;
}

.coming-soon-message p {
    color: #6c757d;
    font-size: 16px;
    line-height: 1.5;
    max-width: 400px;
    margin: 0 auto;
}

/* Field Info */
.field-info {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
    font-style: italic;
}

/* Submit Button */
.submit-button {
    background-color: #5e259b;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 14px 20px;
    font-size: 16px;
    cursor: pointer;
    width: 100%;
    margin-top: 10px;
}

.submit-button:hover {
    background-color: #4a1d7a;
}

/* ID Card Preview Styling */
.id-card-preview {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.id-card {
    width: 250px;
    height: 400px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.id-card-header {
    background-color: #5e259b;
    color: white;
    padding: 15px;
    text-align: center;
}

.university-logo {
    font-weight: bold;
    font-size: 18px;
    margin-bottom: 5px;
}

.card-type {
    font-size: 12px;
    opacity: 0.8;
}

.id-card-photo {
    height: 150px;
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 60px;
    margin: 15px;
    border-radius: 5px;
    position: relative;
}

.id-card-photo .photo-placeholder {
    font-size: 12px;
    margin-top: 10px;
    text-align: center;
}

.photo-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #999;
    text-align: center;
}

.photo-preview i {
    font-size: 40px;
    margin-bottom: 10px;
}

.photo-preview p {
    font-size: 14px;
    margin: 0;
}

.id-card-details {
    padding: 0 15px;
    flex-grow: 1;
}

.id-detail {
    margin-bottom: 15px;
}

.detail-label {
    display: block;
    font-size: 10px;
    color: #666;
    margin-bottom: 2px;
}

.detail-value {
    font-weight: bold;
    font-size: 14px;
}

.id-card-footer {
    padding: 15px;
    background-color: #f5f5f5;
    text-align: center;
}

.barcode {
    font-family: monospace;
    font-size: 14px;
    letter-spacing: -1px;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .id-card-container {
        flex-direction: column;
    }

    .id-card-preview-section {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .user-type-options {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .user-type-label {
        padding: 15px;
    }

    .user-type-icon {
        font-size: 20px;
        margin-right: 12px;
        min-width: 35px;
    }

    .user-type-content h3 {
        font-size: 16px;
    }

    .user-type-content p {
        font-size: 13px;
    }
}

@media (max-width: 576px) {
    .photo-upload-container {
        flex-direction: column;
        align-items: flex-start;
    }

    .photo-preview {
        width: 100%;
    }

    .upload-button {
        width: 100%;
    }

    .user-type-selection {
        padding: 15px;
    }

    .coming-soon-message {
        padding: 30px 15px;
    }

    .coming-soon-icon {
        font-size: 36px;
    }

    .coming-soon-message h3 {
        font-size: 20px;
    }

    .coming-soon-message p {
        font-size: 14px;
    }
}
