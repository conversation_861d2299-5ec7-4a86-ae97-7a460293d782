/* Create ID Page Styles */
.page-header {
    margin-bottom: 30px;
}

.page-header h1 {
    font-size: 24px;
    margin-bottom: 5px;
}

.page-header p {
    color: #666;
}

.id-card-container {
    display: flex;
    gap: 30px;
}

.id-card-form-section {
    flex: 1;
    background-color: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.id-card-preview-section {
    width: 350px;
    background-color: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.section-header {
    margin-bottom: 20px;
}

.section-header h2 {
    font-size: 18px;
    margin-bottom: 5px;
}

.section-header p {
    color: #666;
    font-size: 14px;
}

/* Form Styling */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
}

.form-group select {
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 15px center;
    background-size: 15px;
}

/* Photo Upload Styling */
.photo-upload-container {
    display: flex;
    align-items: center;
    gap: 20px;
}

.photo-preview {
    width: 120px;
    height: 150px;
    background-color: #f5f5f5;
    border: 1px dashed #ddd;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 40px;
}

.upload-button {
    flex: 1;
}

.upload-label {
    display: inline-block;
    background-color: #5e259b;
    color: white;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    margin-bottom: 10px;
}

.upload-label:hover {
    background-color: #4a1d7a;
}

.upload-label i {
    margin-right: 5px;
}

.file-info {
    font-size: 12px;
    color: #666;
}

/* Checkbox Styling */
.checkbox-group {
    display: flex;
    align-items: center;
}

.checkbox-group input[type="checkbox"] {
    margin-right: 10px;
}

/* User Type Selection */
.user-type-selection {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 10px;
    border: 2px solid #e9ecef;
}

.user-type-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.user-type-option {
    position: relative;
}

.user-type-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.user-type-label {
    display: flex;
    align-items: center;
    padding: 20px;
    background-color: white;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    height: 100%;
}

.user-type-label:hover {
    border-color: #5e259b;
    box-shadow: 0 2px 10px rgba(94, 37, 155, 0.1);
}

.user-type-option input[type="radio"]:checked + .user-type-label {
    border-color: #5e259b;
    background-color: #f8f4ff;
    box-shadow: 0 4px 15px rgba(94, 37, 155, 0.2);
}

.user-type-icon {
    margin-right: 15px;
    font-size: 24px;
    color: #5e259b;
    min-width: 40px;
    text-align: center;
}

.user-type-content h3 {
    margin: 0 0 5px 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.user-type-content p {
    margin: 0;
    color: #666;
    font-size: 14px;
    line-height: 1.4;
}

/* User Form Sections */
.user-form-section {
    margin-top: 20px;
}

/* Coming Soon Message */
.coming-soon-message {
    text-align: center;
    padding: 40px 20px;
    background-color: #f8f9fa;
    border-radius: 10px;
    border: 2px dashed #dee2e6;
}

.coming-soon-icon {
    font-size: 48px;
    color: #6c757d;
    margin-bottom: 20px;
}

.coming-soon-message h3 {
    color: #495057;
    margin-bottom: 10px;
    font-size: 24px;
}

.coming-soon-message p {
    color: #6c757d;
    font-size: 16px;
    line-height: 1.5;
    max-width: 400px;
    margin: 0 auto;
}

/* Field Info */
.field-info {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
    font-style: italic;
}

/* Simplified Capture Sections */
.capture-section {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
    background-color: #f8f9fa;
    margin-bottom: 20px;
}

.capture-instructions {
    text-align: center;
    margin-bottom: 20px;
}

.capture-instructions p {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 15px;
    line-height: 1.5;
}

.capture-btn {
    background: linear-gradient(135deg, #5e259b, #8a3df9);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 15px rgba(94, 37, 155, 0.3);
}

.capture-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(94, 37, 155, 0.4);
}

.capture-btn.secondary {
    background-color: #6c757d;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.capture-btn.secondary:hover {
    background-color: #545b62;
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
}

/* Camera Interface */
.camera-interface {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
}

.camera-container {
    position: relative;
    width: 100%;
    max-width: 400px;
    margin: 0 auto 20px;
    border-radius: 10px;
    overflow: hidden;
    background-color: #000;
}

.camera-container video {
    width: 100%;
    height: auto;
    display: block;
}

.camera-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.camera-frame {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    height: 60%;
    border: 3px solid #5e259b;
    border-radius: 10px;
    box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.3);
}

.camera-frame.face-frame {
    width: 60%;
    height: 70%;
    border-radius: 50%;
}

.camera-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.camera-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.camera-btn.capture-btn {
    background-color: #5e259b;
    color: white;
}

.camera-btn.capture-btn:hover {
    background-color: #4a1d7a;
    transform: translateY(-2px);
}

.camera-btn.cancel-btn {
    background-color: #6c757d;
    color: white;
}

.camera-btn.cancel-btn:hover {
    background-color: #545b62;
}

.camera-btn.capture {
    background-color: #5e259b;
    color: white;
}

.camera-btn.capture:hover {
    background-color: #4a1d7a;
}

.camera-btn.cancel {
    background-color: #6c757d;
    color: white;
}

.camera-btn.cancel:hover {
    background-color: #545b62;
}

/* Photo Preview */
.photo-preview {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.preview-image {
    width: 100%;
    max-width: 300px;
    height: 200px;
    margin: 0 auto 15px;
    border-radius: 10px;
    overflow: hidden;
    border: 2px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
}

.preview-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.preview-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.capture-success {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #28a745;
    font-weight: 600;
    font-size: 14px;
}

.capture-success i {
    font-size: 16px;
}

/* ID Card Preview Fixes */
.id-card-photo .photo-icon {
    font-size: 24px;
    color: #6c757d;
    margin-bottom: 5px;
}

.id-card-photo .photo-placeholder {
    font-size: 10px;
    color: #6c757d;
    margin: 0;
    text-align: center;
}

/* Photo Preview Section */
.photo-preview-section {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.captured-photo-preview {
    width: 100%;
    max-width: 300px;
    height: 200px;
    margin: 0 auto 15px;
    border-radius: 10px;
    overflow: hidden;
    border: 2px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
}

.captured-photo-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.preview-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.photo-status {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #28a745;
    font-weight: 600;
    font-size: 14px;
}

.photo-status i {
    font-size: 16px;
}

.capture-controls {
    margin: 20px 0;
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.capture-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: #5e259b;
    color: white;
}

.capture-btn:hover {
    background-color: #4a1d7a;
    transform: translateY(-2px);
}

.capture-btn.secondary {
    background-color: #6c757d;
}

.capture-btn.secondary:hover {
    background-color: #545b62;
}

.capture-info {
    font-size: 14px;
    color: #6c757d;
    margin-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

/* Facial Verification Section */
.facial-verification-section {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
    background-color: #f8f9fa;
    margin-top: 20px;
}

.verification-header {
    text-align: center;
    margin-bottom: 20px;
}

.verification-header h4 {
    color: #5e259b;
    margin-bottom: 8px;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.verification-header p {
    color: #6c757d;
    margin: 0;
    font-size: 14px;
}

.verification-status-display {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px 15px;
    background-color: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.status-icon {
    font-size: 12px;
    transition: all 0.3s ease;
}

.status-icon.pending {
    color: #6c757d;
}

.status-icon.completed {
    color: #28a745;
}

.status-icon.processing {
    color: #ffc107;
    animation: pulse 1.5s infinite;
}

.status-item span {
    font-size: 14px;
    font-weight: 500;
    color: #495057;
}

.status-item.completed {
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.status-item.processing {
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Submit Button */
.submit-button {
    background-color: #5e259b;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 14px 20px;
    font-size: 16px;
    cursor: pointer;
    width: 100%;
    margin-top: 10px;
}

.submit-button:hover {
    background-color: #4a1d7a;
}

/* ID Card Preview Styling */
.id-card-preview {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.id-card {
    width: 250px;
    height: 400px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.id-card-header {
    background-color: #5e259b;
    color: white;
    padding: 15px;
    text-align: center;
}

.university-logo {
    font-weight: bold;
    font-size: 18px;
    margin-bottom: 5px;
}

.card-type {
    font-size: 12px;
    opacity: 0.8;
}

.id-card-photo {
    height: 150px;
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 60px;
    margin: 15px;
    border-radius: 5px;
    position: relative;
}

.id-card-photo .photo-placeholder {
    font-size: 12px;
    margin-top: 10px;
    text-align: center;
}

.photo-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #999;
    text-align: center;
}

.photo-preview i {
    font-size: 40px;
    margin-bottom: 10px;
}

.photo-preview p {
    font-size: 14px;
    margin: 0;
}

.id-card-details {
    padding: 0 15px;
    flex-grow: 1;
}

.id-detail {
    margin-bottom: 15px;
}

.detail-label {
    display: block;
    font-size: 10px;
    color: #666;
    margin-bottom: 2px;
}

.detail-value {
    font-weight: bold;
    font-size: 14px;
}

.id-card-footer {
    padding: 15px;
    background-color: #f5f5f5;
    text-align: center;
}

.barcode {
    font-family: monospace;
    font-size: 14px;
    letter-spacing: -1px;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .id-card-container {
        flex-direction: column;
    }

    .id-card-preview-section {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .user-type-options {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .user-type-label {
        padding: 15px;
    }

    .user-type-icon {
        font-size: 20px;
        margin-right: 12px;
        min-width: 35px;
    }

    .user-type-content h3 {
        font-size: 16px;
    }

    .user-type-content p {
        font-size: 13px;
    }
}

@media (max-width: 576px) {
    .photo-upload-container {
        flex-direction: column;
        align-items: flex-start;
    }

    .photo-preview {
        width: 100%;
    }

    .upload-button {
        width: 100%;
    }

    .user-type-selection {
        padding: 15px;
    }

    .coming-soon-message {
        padding: 30px 15px;
    }

    .coming-soon-icon {
        font-size: 36px;
    }

    .coming-soon-message h3 {
        font-size: 20px;
    }

    .coming-soon-message p {
        font-size: 14px;
    }

    .camera-preview-container {
        max-width: 400px;
    }

    .camera-controls {
        flex-direction: column;
        align-items: center;
    }

    .camera-btn {
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }

    .capture-controls {
        flex-direction: column;
        align-items: center;
    }

    .capture-btn {
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }
}

/* Facial Verification Modal */
.facial-verification-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.modal-content {
    background-color: white;
    border-radius: 15px;
    padding: 30px;
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    text-align: center;
    margin-bottom: 25px;
}

.modal-header h2 {
    color: #5e259b;
    margin-bottom: 10px;
    font-size: 24px;
}

.modal-header p {
    color: #6c757d;
    margin: 0;
    font-size: 16px;
}

.verification-camera-container {
    position: relative;
    width: 100%;
    max-width: 400px;
    margin: 0 auto 25px;
    border-radius: 15px;
    overflow: hidden;
    background-color: #000;
}

#verificationVideo {
    width: 100%;
    height: auto;
    display: block;
}

.face-detection-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.verification-status {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: 20px;
    color: white;
}

.status-message {
    text-align: center;
    font-size: 16px;
    margin-bottom: 10px;
    font-weight: 600;
}

.confidence-meter {
    width: 100%;
    height: 8px;
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    overflow: hidden;
}

.confidence-bar {
    height: 100%;
    background: linear-gradient(90deg, #ff4757, #ffa502, #2ed573);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 4px;
}

.verification-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.verification-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.verification-btn.cancel {
    background-color: #dc3545;
    color: white;
}

.verification-btn.cancel:hover {
    background-color: #c82333;
}

.verification-btn.skip {
    background-color: #6c757d;
    color: white;
}

.verification-btn.skip:hover {
    background-color: #545b62;
}

.verification-btn.retry {
    background-color: #5e259b;
    color: white;
}

.verification-btn.retry:hover {
    background-color: #4a1d7a;
}

.verification-info {
    text-align: center;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 10px;
    border: 1px solid #e9ecef;
}

.verification-info p {
    margin: 0;
    color: #6c757d;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

/* Face Detection Boxes */
.face-box {
    position: absolute;
    border: 3px solid #5e259b;
    border-radius: 5px;
    background-color: rgba(94, 37, 155, 0.1);
}

.face-box.matched {
    border-color: #28a745;
    background-color: rgba(40, 167, 69, 0.1);
}

.face-box.unmatched {
    border-color: #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
}

.face-label {
    position: absolute;
    top: -25px;
    left: 0;
    background-color: #5e259b;
    color: white;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 600;
}

.face-label.matched {
    background-color: #28a745;
}

.face-label.unmatched {
    background-color: #dc3545;
}
