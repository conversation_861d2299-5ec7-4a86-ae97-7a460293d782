<?php
/**
 * Update User Settings
 * 
 * This file handles updating user settings.
 */

require_once 'config.php';
require_once 'auth.php';

// Redirect to login if not authenticated
if (!isLoggedIn()) {
    header('Location: ../index.php');
    exit;
}

// Get user ID from session
$userId = $_SESSION['user_id'];

// Check if the form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    $submittedToken = $_POST['csrf_token'] ?? '';
    if (!hash_equals($_SESSION['csrf_token'], $submittedToken)) {
        $_SESSION['settings_error'] = 'Invalid form submission. Please try again.';
        header('Location: ../html/settings.php');
        exit;
    }
    
    $settingsType = $_POST['settings_type'] ?? '';
    
    try {
        $db = getDbConnection();
        
        // Check if user already has settings
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM user_settings WHERE user_id = ?");
        $stmt->execute([$userId]);
        $result = $stmt->fetch();
        $hasSettings = $result['count'] > 0;
        
        // Process based on settings type
        switch ($settingsType) {
            case 'account':
                $email = $_POST['email'] ?? '';
                $phone = $_POST['phone'] ?? '';
                $language = $_POST['language'] ?? 'en';
                $timezone = $_POST['timezone'] ?? 'utc';
                
                // Update email in users table
                if (!empty($email)) {
                    $stmt = $db->prepare("UPDATE users SET email = ? WHERE user_id = ?");
                    $stmt->execute([$email, $userId]);
                }
                
                // Update phone in user_profiles table
                $stmt = $db->prepare("
                    UPDATE user_profiles 
                    SET phone = ?
                    WHERE user_id = ?
                ");
                $stmt->execute([$phone, $userId]);
                
                // Update language and timezone in user_settings table
                if ($hasSettings) {
                    $stmt = $db->prepare("
                        UPDATE user_settings 
                        SET language = ?, timezone = ?, updated_at = NOW()
                        WHERE user_id = ?
                    ");
                    $stmt->execute([$language, $timezone, $userId]);
                } else {
                    $stmt = $db->prepare("
                        INSERT INTO user_settings 
                        (user_id, language, timezone)
                        VALUES (?, ?, ?)
                    ");
                    $stmt->execute([$userId, $language, $timezone]);
                }
                break;
                
            case 'security':
                $currentPassword = $_POST['current_password'] ?? '';
                $newPassword = $_POST['new_password'] ?? '';
                $confirmPassword = $_POST['confirm_password'] ?? '';
                $twoFactorAuth = isset($_POST['two_factor_auth']) ? 1 : 0;
                $loginNotifications = isset($_POST['login_notifications']) ? 1 : 0;
                
                // Update password if provided
                if (!empty($currentPassword) && !empty($newPassword)) {
                    // Verify current password
                    $stmt = $db->prepare("SELECT password FROM users WHERE user_id = ?");
                    $stmt->execute([$userId]);
                    $user = $stmt->fetch();
                    
                    if (!$user || !password_verify($currentPassword, $user['password'])) {
                        $_SESSION['settings_error'] = 'Current password is incorrect.';
                        header('Location: ../html/settings.php');
                        exit;
                    }
                    
                    // Verify new password matches confirmation
                    if ($newPassword !== $confirmPassword) {
                        $_SESSION['settings_error'] = 'New passwords do not match.';
                        header('Location: ../html/settings.php');
                        exit;
                    }
                    
                    // Validate password strength
                    if (strlen($newPassword) < 8 || 
                        !preg_match('/[A-Z]/', $newPassword) || 
                        !preg_match('/[a-z]/', $newPassword) || 
                        !preg_match('/[0-9]/', $newPassword) || 
                        !preg_match('/[^A-Za-z0-9]/', $newPassword)) {
                        
                        $_SESSION['settings_error'] = 'Password must be at least 8 characters long and include at least one uppercase letter, one lowercase letter, one number, and one special character.';
                        header('Location: ../html/settings.php');
                        exit;
                    }
                    
                    // Hash new password and update
                    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                    $stmt = $db->prepare("UPDATE users SET password = ? WHERE user_id = ?");
                    $stmt->execute([$hashedPassword, $userId]);
                    
                    // Log password change
                    logActivity($userId, 'PASSWORD_CHANGE', 'Password changed');
                }
                
                // Update security settings
                if ($hasSettings) {
                    $stmt = $db->prepare("
                        UPDATE user_settings 
                        SET two_factor_auth = ?, login_notifications = ?, updated_at = NOW()
                        WHERE user_id = ?
                    ");
                    $stmt->execute([$twoFactorAuth, $loginNotifications, $userId]);
                } else {
                    $stmt = $db->prepare("
                        INSERT INTO user_settings 
                        (user_id, two_factor_auth, login_notifications)
                        VALUES (?, ?, ?)
                    ");
                    $stmt->execute([$userId, $twoFactorAuth, $loginNotifications]);
                }
                break;
                
            case 'notifications':
                $emailNotifications = isset($_POST['email_notifications']) ? 1 : 0;
                $smsNotifications = isset($_POST['sms_notifications']) ? 1 : 0;
                $browserNotifications = isset($_POST['browser_notifications']) ? 1 : 0;
                $idCardUpdates = isset($_POST['id_card_updates']) ? 1 : 0;
                $documentReminders = isset($_POST['document_reminders']) ? 1 : 0;
                $accountActivity = isset($_POST['account_activity']) ? 1 : 0;
                $newsUpdates = isset($_POST['news_updates']) ? 1 : 0;
                
                if ($hasSettings) {
                    $stmt = $db->prepare("
                        UPDATE user_settings 
                        SET email_notifications = ?, sms_notifications = ?, browser_notifications = ?,
                            id_card_updates = ?, document_reminders = ?, account_activity = ?, news_updates = ?,
                            updated_at = NOW()
                        WHERE user_id = ?
                    ");
                    $stmt->execute([
                        $emailNotifications, $smsNotifications, $browserNotifications,
                        $idCardUpdates, $documentReminders, $accountActivity, $newsUpdates,
                        $userId
                    ]);
                } else {
                    $stmt = $db->prepare("
                        INSERT INTO user_settings 
                        (user_id, email_notifications, sms_notifications, browser_notifications,
                         id_card_updates, document_reminders, account_activity, news_updates)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $userId, $emailNotifications, $smsNotifications, $browserNotifications,
                        $idCardUpdates, $documentReminders, $accountActivity, $newsUpdates
                    ]);
                }
                break;
                
            default:
                $_SESSION['settings_error'] = 'Invalid settings type.';
                header('Location: ../html/settings.php');
                exit;
        }
        
        // Log the activity
        logActivity($userId, 'UPDATE_SETTINGS', 'Updated settings: ' . $settingsType);
        
        // Set success message
        $_SESSION['settings_message'] = 'Settings updated successfully.';
        
        // Redirect back to settings page
        header('Location: ../html/settings.php');
        exit;
        
    } catch (PDOException $e) {
        error_log("Settings Error: " . $e->getMessage());
        $_SESSION['settings_error'] = 'An error occurred while updating your settings. Please try again.';
        header('Location: ../html/settings.php');
        exit;
    }
} else {
    // Not a POST request, redirect to settings page
    header('Location: ../html/settings.php');
    exit;
}
