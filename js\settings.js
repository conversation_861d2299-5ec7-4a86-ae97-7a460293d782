document.addEventListener('DOMContentLoaded', function() {
    // Settings navigation functionality
    const settingsNavItems = document.querySelectorAll('.settings-nav-item');
    const settingsSections = document.querySelectorAll('.settings-section');

    settingsNavItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();

            // Get the target section
            const targetId = this.getAttribute('data-target');
            const targetSection = document.getElementById(targetId);

            // Remove active class from all nav items and sections
            settingsNavItems.forEach(navItem => {
                navItem.classList.remove('active');
            });

            settingsSections.forEach(section => {
                section.classList.remove('active');
            });

            // Add active class to clicked nav item and target section
            this.classList.add('active');
            if (targetSection) {
                targetSection.classList.add('active');
            }

            // Update URL hash
            window.location.hash = this.getAttribute('href');
        });
    });

    // Check for hash in URL and activate corresponding section
    if (window.location.hash) {
        const hash = window.location.hash;
        const navItem = document.querySelector(`a[href="${hash}"]`);

        if (navItem) {
            navItem.click();
        }
    }

    // Email validation for Gmail only
    const emailInput = document.getElementById('email');
    const emailError = document.getElementById('email-error');

    if (emailInput && emailError) {
        emailInput.addEventListener('blur', function() {
            validateEmail();
        });

        emailInput.addEventListener('input', function() {
            if (emailError.style.display === 'block') {
                validateEmail();
            }
        });
    }

    function validateEmail() {
        const email = emailInput.value.trim();
        if (email && !email.endsWith('@gmail.com')) {
            emailInput.classList.add('error');
            emailError.style.display = 'block';
            return false;
        } else {
            emailInput.classList.remove('error');
            emailError.style.display = 'none';
            return true;
        }
    }

    // Password matching validation
    const newPasswordInput = document.getElementById('new-password');
    const confirmPasswordInput = document.getElementById('confirm-new-password');
    const passwordError = document.getElementById('password-error');

    if (newPasswordInput && confirmPasswordInput && passwordError) {
        confirmPasswordInput.addEventListener('blur', function() {
            validatePasswordMatch();
        });

        confirmPasswordInput.addEventListener('input', function() {
            if (passwordError.style.display === 'block') {
                validatePasswordMatch();
            }
        });

        newPasswordInput.addEventListener('input', function() {
            if (passwordError.style.display === 'block') {
                validatePasswordMatch();
            }
        });
    }

    function validatePasswordMatch() {
        const newPassword = newPasswordInput.value;
        const confirmPassword = confirmPasswordInput.value;

        if (newPassword && confirmPassword && newPassword !== confirmPassword) {
            confirmPasswordInput.classList.add('error');
            passwordError.style.display = 'block';
            return false;
        } else {
            confirmPasswordInput.classList.remove('error');
            passwordError.style.display = 'none';
            return true;
        }
    }

    // Form submission validation
    const accountForm = document.getElementById('accountSettingsForm');
    if (accountForm) {
        accountForm.addEventListener('submit', function(e) {
            if (!validateEmail()) {
                e.preventDefault();
                emailInput.focus();
                return;
            }
            // Allow form to submit normally if validation passes
        });
    }

    const securityForm = document.getElementById('securitySettingsForm');
    if (securityForm) {
        securityForm.addEventListener('submit', function(e) {
            const currentPassword = document.getElementById('current-password').value;
            const newPassword = document.getElementById('new-password').value;

            // Validate current password is provided
            if (!currentPassword) {
                e.preventDefault();
                alert('Please enter your current password');
                document.getElementById('current-password').focus();
                return;
            }

            // Validate password match if new password is provided
            if (newPassword && !validatePasswordMatch()) {
                e.preventDefault();
                confirmPasswordInput.focus();
                return;
            }

            // Allow form to submit normally if validation passes
        });
    }

});
