document.addEventListener('DOMContentLoaded', function() {
    // Settings navigation functionality
    const settingsNavItems = document.querySelectorAll('.settings-nav-item');
    const settingsSections = document.querySelectorAll('.settings-section');
    
    settingsNavItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Get the target section
            const targetId = this.getAttribute('data-target');
            const targetSection = document.getElementById(targetId);
            
            // Remove active class from all nav items and sections
            settingsNavItems.forEach(navItem => {
                navItem.classList.remove('active');
            });
            
            settingsSections.forEach(section => {
                section.classList.remove('active');
            });
            
            // Add active class to clicked nav item and target section
            this.classList.add('active');
            targetSection.classList.add('active');
            
            // Update URL hash
            window.location.hash = this.getAttribute('href');
        });
    });
    
    // Check for hash in URL and activate corresponding section
    if (window.location.hash) {
        const hash = window.location.hash;
        const navItem = document.querySelector(`a[href="${hash}"]`);
        
        if (navItem) {
            navItem.click();
        }
    }
    
    // Account settings form submission
    const accountSettingsForm = document.getElementById('accountSettingsForm');
    
    if (accountSettingsForm) {
        accountSettingsForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form data
            const email = document.getElementById('email').value.trim();
            const phone = document.getElementById('phone').value.trim();
            const language = document.getElementById('language').value;
            const timezone = document.getElementById('timezone').value;
            
            // Validate email
            if (!email || !isValidEmail(email)) {
                alert('Please enter a valid email address');
                document.getElementById('email').focus();
                return;
            }
            
            // Here you would typically send this data to a server
            // For now, just show an alert
            alert('Account settings updated successfully!');
        });
    }
    
    // Security settings form submission
    const securitySettingsForm = document.getElementById('securitySettingsForm');
    
    if (securitySettingsForm) {
        securitySettingsForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form data
            const currentPassword = document.getElementById('current-password').value;
            const newPassword = document.getElementById('new-password').value;
            const confirmNewPassword = document.getElementById('confirm-new-password').value;
            
            // Validate passwords
            if (!currentPassword) {
                alert('Please enter your current password');
                document.getElementById('current-password').focus();
                return;
            }
            
            if (newPassword) {
                if (newPassword.length < 8) {
                    alert('New password must be at least 8 characters long');
                    document.getElementById('new-password').focus();
                    return;
                }
                
                if (newPassword !== confirmNewPassword) {
                    alert('New passwords do not match');
                    document.getElementById('confirm-new-password').focus();
                    return;
                }
            }
            
            // Here you would typically send this data to a server
            // For now, just show an alert
            alert('Security settings updated successfully!');
        });
    }
    
    // Notification settings form submission
    const notificationSettingsForm = document.getElementById('notificationSettingsForm');
    
    if (notificationSettingsForm) {
        notificationSettingsForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form data
            const emailNotifications = document.getElementById('emailNotifications').checked;
            const smsNotifications = document.getElementById('smsNotifications').checked;
            const browserNotifications = document.getElementById('browserNotifications').checked;
            
            // Here you would typically send this data to a server
            // For now, just show an alert
            alert('Notification preferences saved successfully!');
        });
    }
    
    // Helper function to validate email
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    // Two-factor authentication toggle
    const twoFactorAuth = document.getElementById('twoFactorAuth');
    
    if (twoFactorAuth) {
        twoFactorAuth.addEventListener('change', function() {
            if (this.checked) {
                // In a real application, this would open a modal to set up 2FA
                // For now, just show an alert
                alert('This would open a modal to set up two-factor authentication');
            }
        });
    }
});
