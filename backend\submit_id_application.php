<?php
/**
 * Submit ID Card Application
 *
 * This file handles the submission of ID card applications with photo uploads.
 */

require_once 'config.php';
require_once 'auth.php';

// Redirect to login if not authenticated
if (!isLoggedIn()) {
    header('Location: ../index.php');
    exit;
}

// Get user ID from session
$userId = $_SESSION['user_id'];

// Check if the form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    $submittedToken = $_POST['csrf_token'] ?? '';
    if (!hash_equals($_SESSION['csrf_token'], $submittedToken)) {
        $_SESSION['createid_error'] = 'Invalid form submission. Please try again.';
        header('Location: ../html/createid.php');
        exit;
    }

    // Get form data
    $userType = $_POST['user_type'] ?? '';
    $fullName = $_POST['full_name'] ?? '';
    $studentId = $_POST['student_id'] ?? '';
    $department = $_POST['department'] ?? '';
    $program = $_POST['program'] ?? '';
    $emergencyContact = $_POST['emergency_contact'] ?? '';
    $applicationType = $_POST['application_type'] ?? 'NEW';

    // Validate user type
    if (empty($userType) || !in_array($userType, ['new', 'existing'])) {
        $_SESSION['createid_error'] = 'Please select a valid user type.';
        header('Location: ../html/createid.php');
        exit;
    }

    // Validate required fields for both user types
    if (empty($fullName) || empty($studentId) || empty($department) || empty($program) || empty($emergencyContact)) {
        $_SESSION['createid_error'] = 'All required fields must be filled out.';
        header('Location: ../html/createid.php');
        exit;
    }

    // Get facial recognition data
    $facialVerificationPassed = $_POST['facial_verification_passed'] ?? false;
    $facialVerificationSkipped = $_POST['facial_verification_skipped'] ?? false;
    $facialDescriptor = $_POST['facial_descriptor'] ?? '';
    $capturedPhotoData = $_POST['id_card_photo_data'] ?? '';
    $facePhotoData = $_POST['face_photo_data'] ?? '';

    // Validate captured photo data
    if (empty($capturedPhotoData)) {
        $_SESSION['createid_error'] = 'ID card photo is required. Please capture your National ID document.';
        header('Location: ../html/createid.php');
        exit;
    }

    if (empty($facePhotoData)) {
        $_SESSION['createid_error'] = 'Face photo is required. Please capture your face photo.';
        header('Location: ../html/createid.php');
        exit;
    }

    // Handle document upload
    $documentPath = null;
    if (isset($_FILES['proof_of_registration']) && $_FILES['proof_of_registration']['error'] === UPLOAD_ERR_OK) {
        $uploadedFile = $_FILES['proof_of_registration'];

        // Validate file type
        $allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
        $fileType = $uploadedFile['type'];
        $fileExtension = strtolower(pathinfo($uploadedFile['name'], PATHINFO_EXTENSION));
        $allowedExtensions = ['pdf', 'jpg', 'jpeg', 'png'];

        if (!in_array($fileType, $allowedTypes) || !in_array($fileExtension, $allowedExtensions)) {
            $_SESSION['createid_error'] = 'Invalid document format. Please upload a PDF, JPG, or PNG file.';
            header('Location: ../html/createid.php');
            exit;
        }

        // Validate file size (max 5MB)
        $maxSize = 5 * 1024 * 1024; // 5MB in bytes
        if ($uploadedFile['size'] > $maxSize) {
            $_SESSION['createid_error'] = 'Document file size exceeds the limit of 5MB.';
            header('Location: ../html/createid.php');
            exit;
        }

        // Generate unique filename for document
        $newDocumentFilename = 'proof_' . $userId . '_' . uniqid() . '.' . $fileExtension;
        $documentUploadPath = UPLOAD_DIR . $newDocumentFilename;

        // Create uploads directory if it doesn't exist
        if (!file_exists(UPLOAD_DIR)) {
            mkdir(UPLOAD_DIR, 0755, true);
        }

        // Move uploaded file
        if (!move_uploaded_file($uploadedFile['tmp_name'], $documentUploadPath)) {
            $_SESSION['createid_error'] = 'Failed to save document. Please try again.';
            header('Location: ../html/createid.php');
            exit;
        }

        // Store the relative path for database
        $documentPath = '../uploads/' . $newDocumentFilename;
    } else {
        $_SESSION['createid_error'] = 'Proof of registration document is required.';
        header('Location: ../html/createid.php');
        exit;
    }

    // Process base64 image data
    $idCardPhotoPath = null;
    if (!empty($capturedPhotoData)) {
        // Validate base64 image format
        if (!preg_match('/^data:image\/(jpeg|png);base64,/', $capturedPhotoData)) {
            $_SESSION['createid_error'] = 'Invalid photo format. Please capture your photo again.';
            header('Location: ../html/createid.php');
            exit;
        }

        // Extract base64 data
        $imageData = preg_replace('/^data:image\/(jpeg|png);base64,/', '', $capturedPhotoData);
        $imageData = base64_decode($imageData);

        // Validate decoded image size (max 2MB)
        $maxSize = 2 * 1024 * 1024; // 2MB in bytes
        if (strlen($imageData) > $maxSize) {
            $_SESSION['createid_error'] = 'Photo file size exceeds the limit of 2MB.';
            header('Location: ../html/createid.php');
            exit;
        }

        // Generate unique filename for ID card photo
        $fileExtension = 'jpg'; // Default to JPG for captured photos
        $newFilename = 'idcard_' . $userId . '_' . uniqid() . '.' . $fileExtension;
        $uploadPath = UPLOAD_DIR . $newFilename;

        // Create uploads directory if it doesn't exist
        if (!file_exists(UPLOAD_DIR)) {
            mkdir(UPLOAD_DIR, 0755, true);
        }

        // Save the image file
        if (file_put_contents($uploadPath, $imageData) === false) {
            $_SESSION['createid_error'] = 'Failed to save ID card photo. Please try again.';
            header('Location: ../html/createid.php');
            exit;
        }

        // Store the relative path for database
        $idCardPhotoPath = '../uploads/' . $newFilename;
    }

    try {
        $db = getDbConnection();

        // Check if user already has a pending or approved application
        $stmt = $db->prepare("
            SELECT application_id, status FROM id_applications
            WHERE user_id = ? AND status IN ('PENDING', 'APPROVED')
        ");
        $stmt->execute([$userId]);

        if ($stmt->rowCount() > 0) {
            // Delete the uploaded file since we won't use it
            if (file_exists($uploadPath)) {
                unlink($uploadPath);
            }
            $_SESSION['createid_error'] = 'You already have a pending or approved ID card application.';
            header('Location: ../html/createid.php');
            exit;
        }

        // Update user profile with the new information
        $stmt = $db->prepare("
            UPDATE user_profiles
            SET full_name = ?, student_id = ?, department = ?, program = ?, emergency_contact = ?
            WHERE user_id = ?
        ");
        $stmt->execute([
            $fullName, $studentId, $department, $program, $emergencyContact, $userId
        ]);

        // Create new ID card application with facial verification status and document
        $stmt = $db->prepare("
            INSERT INTO id_applications (user_id, application_type, status, id_card_photo, proof_of_registration, submission_date, user_type)
            VALUES (?, ?, 'PENDING', ?, ?, NOW(), ?)
        ");
        $stmt->execute([$userId, $applicationType, $idCardPhotoPath, $documentPath, $userType]);

        $applicationId = $db->lastInsertId();

        // Create notification for the user
        $stmt = $db->prepare("
            INSERT INTO notifications (user_id, title, message, notification_type, created_at, is_read)
            VALUES (?, 'ID Card Application Submitted', 'Your ID card application has been submitted and is pending review.', 'ID_CARD', NOW(), 0)
        ");
        $stmt->execute([$userId]);

        // Log the application submission with facial verification status
        $logMessage = 'User submitted ID card application with facial recognition';
        if ($facialVerificationPassed) {
            $logMessage .= ' (verification passed)';
        } elseif ($facialVerificationSkipped) {
            $logMessage .= ' (verification skipped)';
        }
        logActivity($userId, 'ID_APPLICATION', $logMessage);

        // Send email notification if enabled
        require_once 'email_notifications.php';
        sendIdCardNotification($userId, 'PENDING');

        // Set success message based on verification status
        if ($facialVerificationPassed) {
            $_SESSION['createid_message'] = 'ID card application submitted successfully with facial verification! You will be notified when it is reviewed.';
        } elseif ($facialVerificationSkipped) {
            $_SESSION['createid_message'] = 'ID card application submitted successfully! Note: Facial verification was skipped. You will be notified when it is reviewed.';
        } else {
            $_SESSION['createid_message'] = 'ID card application submitted successfully! You will be notified when it is reviewed.';
        }

        // Redirect to dashboard
        header('Location: ../html/dashboard.php');
        exit;

    } catch (PDOException $e) {
        error_log("ID Card Application Error: " . $e->getMessage());

        // Delete the uploaded file if database operation failed
        if (file_exists($uploadPath)) {
            unlink($uploadPath);
        }

        $_SESSION['createid_error'] = 'Application submission failed. Please try again later.';
        header('Location: ../html/createid.php');
        exit;
    }
} else {
    // Not a POST request, redirect to create ID page
    header('Location: ../html/createid.php');
    exit;
}
?>
