<?php
// Theme synchronization script for all pages
// This file should be included after user authentication and database connection

try {
    // Get user theme settings if not already loaded
    if (!isset($userTheme)) {
        $stmt = $db->prepare("
            SELECT theme FROM user_settings
            WHERE user_id = ?
        ");
        $stmt->execute([$userId]);
        $userSettings = $stmt->fetch();
        $userTheme = $userSettings ? $userSettings['theme'] : 'light';
    }
} catch (PDOException $e) {
    error_log("Theme Sync Error: " . $e->getMessage());
    $userTheme = 'light';
}
?>
<script>
    // Sync database theme with localStorage
    document.addEventListener('DOMContentLoaded', function() {
        const dbTheme = '<?php echo $userTheme; ?>';
        if (dbTheme && dbTheme !== window.ThemeLoader.getCurrentTheme()) {
            window.ThemeLoader.setTheme(dbTheme);
        }
    });
</script>
