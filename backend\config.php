<?php
/**
 * Database Configuration
 * 
 * This file contains the database connection settings for the Student ID Application.
 */

// Database credentials
define('DB_HOST', 'localhost');
define('DB_NAME', 'student_id_app');
define('DB_USER', 'root');
define('DB_PASS', '');

// Application settings
define('APP_NAME', 'Student ID Application');
define('APP_URL', 'http://localhost/Student_ID_Application');
define('APP_VERSION', '1.0.0');

// File upload settings
define('UPLOAD_DIR', '../uploads/');
define('MAX_FILE_SIZE', 2 * 1024 * 1024); // 2MB
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx']);

// Session settings
define('SESSION_NAME', 'student_id_app_session');
define('SESSION_LIFETIME', 3600); // 1 hour

/**
 * Database Connection
 * 
 * Creates a PDO connection to the database.
 * 
 * @return PDO Database connection
 */
function getDbConnection() {
    try {
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ];
        
        return new PDO($dsn, DB_USER, DB_PASS, $options);
    } catch (PDOException $e) {
        // Log the error and display a user-friendly message
        error_log("Database Connection Error: " . $e->getMessage());
        die("Could not connect to the database. Please try again later.");
    }
}

/**
 * Initialize Application
 * 
 * Sets up the application environment.
 */
function initApp() {
    // Start or resume session
    session_name(SESSION_NAME);
    session_start();
    
    // Set session cookie parameters
    session_set_cookie_params([
        'lifetime' => SESSION_LIFETIME,
        'path' => '/',
        'domain' => '',
        'secure' => true,
        'httponly' => true,
        'samesite' => 'Lax'
    ]);
    
    // Set error reporting based on environment
    if (defined('ENVIRONMENT') && ENVIRONMENT === 'development') {
        error_reporting(E_ALL);
        ini_set('display_errors', 1);
    } else {
        error_reporting(0);
        ini_set('display_errors', 0);
    }
    
    // Create upload directory if it doesn't exist
    if (!file_exists(UPLOAD_DIR)) {
        mkdir(UPLOAD_DIR, 0755, true);
    }
}

// Initialize the application
initApp();
