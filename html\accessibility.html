<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Display & Accessibility - Student Portal</title>
    <link rel="stylesheet" href="../css/dashboard.css">
    <link rel="stylesheet" href="../css/accessibility.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <a href="../index.html">Student Portal</a>
            </div>

            <div class="user-profile">
                <div class="avatar">JD</div>
                <div class="user-info">
                    <div class="user-name"><PERSON></div>
                    <div class="user-id">ID: STUD1245</div>
                </div>
            </div>

            <nav class="sidebar-nav">
                <a href="dashboard.html" class="nav-item">
                    <i class="fas fa-home"></i> Dashboard
                </a>
                <a href="profile.html" class="nav-item">
                    <i class="fas fa-user"></i> Profile
                </a>
                <a href="createid.html" class="nav-item">
                    <i class="fas fa-id-card"></i> Create New ID
                </a>
                <a href="documents.html" class="nav-item">
                    <i class="fas fa-file-alt"></i> Documents
                </a>
                <a href="accessibility.html" class="nav-item active">
                    <i class="fas fa-universal-access"></i> Display & Accessibility
                </a>
                <a href="settings.html" class="nav-item">
                    <i class="fas fa-cog"></i> Settings
                </a>
                <a href="../backend/logout.php" class="nav-item logout-btn">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="top-bar">
                <button type="button" class="menu-toggle" title="Toggle Menu">
                    <i class="fas fa-bars"></i>
                </button>

                <div class="search-bar">
                    <input type="text" placeholder="Search...">
                    <i class="fas fa-search"></i>
                </div>

                <div class="top-icons">
                    <div class="icon-badge">
                        <i class="fas fa-bell"></i>
                        <span class="badge">1</span>
                    </div>
                    <div class="icon-badge">
                        <i class="fas fa-envelope"></i>
                        <span class="badge">2</span>
                    </div>
                </div>
            </div>

            <div class="dashboard-content">
                <div class="page-header">
                    <h1>Display & Accessibility</h1>
                    <p>Customize your experience to suit your needs</p>
                </div>

                <div class="accessibility-container">
                    <div class="accessibility-section">
                        <div class="section-header">
                            <h2>Display Preferences</h2>
                            <p>Adjust how content appears on your screen</p>
                        </div>

                        <form id="displayPreferencesForm">
                            <div class="form-group">
                                <label for="theme">Color Theme</label>
                                <div class="theme-options">
                                    <div class="theme-option">
                                        <input type="radio" name="theme" id="theme-light" value="light" checked>
                                        <label for="theme-light" class="theme-label light-theme">
                                            <div class="theme-preview"></div>
                                            <span>Light</span>
                                        </label>
                                    </div>

                                    <div class="theme-option">
                                        <input type="radio" name="theme" id="theme-dark" value="dark">
                                        <label for="theme-dark" class="theme-label dark-theme">
                                            <div class="theme-preview"></div>
                                            <span>Dark</span>
                                        </label>
                                    </div>

                                    <div class="theme-option">
                                        <input type="radio" name="theme" id="theme-purple" value="purple">
                                        <label for="theme-purple" class="theme-label purple-theme">
                                            <div class="theme-preview"></div>
                                            <span>Purple</span>
                                        </label>
                                    </div>

                                    <div class="theme-option">
                                        <input type="radio" name="theme" id="theme-blue" value="blue">
                                        <label for="theme-blue" class="theme-label blue-theme">
                                            <div class="theme-preview"></div>
                                            <span>Blue</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="fontSize">Text Size</label>
                                <div class="range-slider">
                                    <input type="range" id="fontSize" min="80" max="150" value="100" class="slider">
                                    <div class="range-labels">
                                        <span>A</span>
                                        <span>A</span>
                                        <span>A</span>
                                    </div>
                                    <div class="range-value">100%</div>
                                </div>
                            </div>

                            <div class="form-group toggle-group">
                                <label class="toggle-label">
                                    <span>Reduce Animations</span>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="reduceAnimations">
                                        <span class="toggle-slider"></span>
                                    </div>
                                </label>
                                <p class="setting-description">Minimize motion effects throughout the interface</p>
                            </div>

                            <div class="form-group toggle-group">
                                <label class="toggle-label">
                                    <span>High Contrast Mode</span>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="highContrast">
                                        <span class="toggle-slider"></span>
                                    </div>
                                </label>
                                <p class="setting-description">Increase contrast for better visibility</p>
                            </div>
                        </form>
                    </div>

                    <div class="accessibility-section">
                        <div class="section-header">
                            <h2>Reading & Navigation</h2>
                            <p>Customize how you interact with content</p>
                        </div>

                        <form id="readingPreferencesForm">
                            <div class="form-group">
                                <label for="fontFamily">Font Style</label>
                                <select id="fontFamily">
                                    <option value="default" selected>Default (Arial)</option>
                                    <option value="dyslexic">OpenDyslexic</option>
                                    <option value="serif">Serif</option>
                                    <option value="monospace">Monospace</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="lineSpacing">Line Spacing</label>
                                <div class="range-slider">
                                    <input type="range" id="lineSpacing" min="100" max="200" value="150" class="slider">
                                    <div class="range-value">150%</div>
                                </div>
                            </div>

                            <div class="form-group toggle-group">
                                <label class="toggle-label">
                                    <span>Screen Reader Support</span>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="screenReader">
                                        <span class="toggle-slider"></span>
                                    </div>
                                </label>
                                <p class="setting-description">Optimize content for screen readers</p>
                            </div>

                            <div class="form-group toggle-group">
                                <label class="toggle-label">
                                    <span>Keyboard Navigation</span>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="keyboardNav" checked>
                                        <span class="toggle-slider"></span>
                                    </div>
                                </label>
                                <p class="setting-description">Enable enhanced keyboard navigation</p>
                            </div>
                        </form>
                    </div>

                    <div class="accessibility-section">
                        <div class="section-header">
                            <h2>Content Preferences</h2>
                            <p>Control how media and content is displayed</p>
                        </div>

                        <form id="contentPreferencesForm">
                            <div class="form-group toggle-group">
                                <label class="toggle-label">
                                    <span>Autoplay Videos</span>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="autoplayVideos">
                                        <span class="toggle-slider"></span>
                                    </div>
                                </label>
                            </div>

                            <div class="form-group toggle-group">
                                <label class="toggle-label">
                                    <span>Show Image Descriptions</span>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="imageDescriptions" checked>
                                        <span class="toggle-slider"></span>
                                    </div>
                                </label>
                                <p class="setting-description">Display alternative text for images</p>
                            </div>

                            <div class="form-group toggle-group">
                                <label class="toggle-label">
                                    <span>Focus Mode</span>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="focusMode">
                                        <span class="toggle-slider"></span>
                                    </div>
                                </label>
                                <p class="setting-description">Reduce distractions by simplifying the interface</p>
                            </div>

                            <button type="submit" class="save-button">Save All Preferences</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../js/dashboard.js"></script>
    <script src="../js/accessibility.js"></script>
</body>
</html>
