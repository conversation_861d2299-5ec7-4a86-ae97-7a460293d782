/**
 * Facial Verification System for Student ID Application
 * Compares ID document face with captured face photo using Face-API.js
 */

class FacialVerificationSystem {
    constructor() {
        this.modelsLoaded = false;
        this.verificationInProgress = false;
        this.verificationStatus = {
            new: { passed: false, attempted: false },
            existing: { passed: false, attempted: false }
        };
        this.similarityThreshold = 0.6; // 60% similarity required

        // DOM elements
        this.elements = {
            // Verify buttons
            newUserVerifyBtn: null,
            existingUserVerifyBtn: null,

            // Form elements
            newUserForm: document.getElementById('idCardForm'),
            existingUserForm: document.getElementById('existingUserIdCardForm'),

            // Submit buttons
            newUserSubmitBtn: null,
            existingUserSubmitBtn: null,

            // Status displays
            newUserStatus: null,
            existingUserStatus: null
        };

        this.init();
    }

    async init() {
        console.log('Initializing Facial Verification System...');

        // Load Face-API.js models
        await this.loadModels();

        // Setup UI elements
        this.setupUI();

        // Setup event listeners
        this.setupEventListeners();

        console.log('Facial Verification System initialized successfully');
    }

    async loadModels() {
        try {
            console.log('Loading Face-API.js models...');

            const MODEL_URL = './js/models';

            // Load required models in parallel
            await Promise.all([
                faceapi.nets.ssdMobilenetv1.loadFromUri(MODEL_URL),
                faceapi.nets.faceLandmark68Net.loadFromUri(MODEL_URL),
                faceapi.nets.faceRecognitionNet.loadFromUri(MODEL_URL)
            ]);

            this.modelsLoaded = true;
            console.log('Face-API.js models loaded successfully');

            // Enable verify buttons once models are loaded
            this.updateVerifyButtonStates();

        } catch (error) {
            console.error('Error loading Face-API.js models:', error);
            this.showError('Failed to load facial recognition models. Please refresh the page.');
        }
    }

    setupUI() {
        // Create verify buttons for both forms
        this.createVerifyButtons();

        // Get submit buttons
        this.elements.newUserSubmitBtn = this.elements.newUserForm?.querySelector('.submit-button');
        this.elements.existingUserSubmitBtn = this.elements.existingUserForm?.querySelector('.submit-button');

        // Initially disable submit buttons
        this.disableFormSubmission();
    }

    createVerifyButtons() {
        // Create verify button for New User form
        const newUserEmergencyContact = document.getElementById('emergencyContact')?.closest('.form-group');
        if (newUserEmergencyContact) {
            const newUserVerifyBtn = this.createVerifyButton('new');
            newUserEmergencyContact.parentNode.insertBefore(newUserVerifyBtn, newUserEmergencyContact);
            this.elements.newUserVerifyBtn = newUserVerifyBtn.querySelector('.verify-btn');
            this.elements.newUserStatus = newUserVerifyBtn.querySelector('.verification-status');
        }

        // Create verify button for Existing User form
        const existingUserEmergencyContact = document.getElementById('existingEmergencyContact')?.closest('.form-group');
        if (existingUserEmergencyContact) {
            const existingUserVerifyBtn = this.createVerifyButton('existing');
            existingUserEmergencyContact.parentNode.insertBefore(existingUserVerifyBtn, existingUserEmergencyContact);
            this.elements.existingUserVerifyBtn = existingUserVerifyBtn.querySelector('.verify-btn');
            this.elements.existingUserStatus = existingUserVerifyBtn.querySelector('.verification-status');
        }
    }

    createVerifyButton(userType) {
        const verifySection = document.createElement('div');
        verifySection.className = 'form-group verification-section';
        verifySection.innerHTML = `
            <div class="verification-container">
                <button type="button" class="verify-btn capture-btn" data-user-type="${userType}" disabled>
                    <i class="fas fa-shield-check"></i> Verify Identity
                </button>
                <div class="verification-status" id="${userType}VerificationStatus" style="display: none;">
                    <div class="status-message"></div>
                    <div class="status-icon"></div>
                </div>
            </div>
        `;
        return verifySection;
    }

    setupEventListeners() {
        // Verify button click handlers
        if (this.elements.newUserVerifyBtn) {
            this.elements.newUserVerifyBtn.addEventListener('click', () => this.performVerification('new'));
        }

        if (this.elements.existingUserVerifyBtn) {
            this.elements.existingUserVerifyBtn.addEventListener('click', () => this.performVerification('existing'));
        }

        // Form submission handlers
        if (this.elements.newUserForm) {
            this.elements.newUserForm.addEventListener('submit', (e) => this.handleFormSubmission(e, 'new'));
        }

        if (this.elements.existingUserForm) {
            this.elements.existingUserForm.addEventListener('submit', (e) => this.handleFormSubmission(e, 'existing'));
        }

        // Listen for photo capture events to update button states
        document.addEventListener('photoCaptured', (e) => {
            this.updateVerifyButtonStates();
            // Reset verification status when photos are retaken
            if (e.detail.retaken || e.detail.photoData === null) {
                this.resetVerificationStatus(e.detail.userType);
            }
        });
    }

    async performVerification(userType) {
        if (!this.modelsLoaded) {
            this.showError('Facial recognition models are still loading. Please wait...');
            return;
        }

        if (this.verificationInProgress) {
            return;
        }

        try {
            this.verificationInProgress = true;
            this.verificationStatus[userType].attempted = true;
            this.updateVerifyButtonState(userType, 'loading');

            // Get captured images
            const idImageData = this.getCapturedIdImage(userType);
            const faceImageData = this.getCapturedFaceImage(userType);

            if (!idImageData || !faceImageData) {
                throw new Error('Both ID document and face photos must be captured before verification');
            }

            // Convert base64 to image elements
            const idImage = await this.base64ToImage(idImageData);
            const faceImage = await this.base64ToImage(faceImageData);

            // Extract facial descriptors
            const idDescriptor = await this.extractFaceDescriptor(idImage, 'ID document');
            const faceDescriptor = await this.extractFaceDescriptor(faceImage, 'face photo');

            // Compare descriptors
            const similarity = this.compareFaceDescriptors(idDescriptor, faceDescriptor);
            const isMatch = similarity >= this.similarityThreshold;

            if (isMatch) {
                this.handleVerificationSuccess(userType, similarity);
            } else {
                this.handleVerificationFailure(userType, similarity);
            }

        } catch (error) {
            console.error('Verification error:', error);
            this.handleVerificationError(userType, error.message);
        } finally {
            this.verificationInProgress = false;
        }
    }

    async extractFaceDescriptor(imageElement, imageType) {
        try {
            // Detect face with landmarks and descriptor
            const detection = await faceapi
                .detectSingleFace(imageElement)
                .withFaceLandmarks()
                .withFaceDescriptor();

            if (!detection) {
                throw new Error(`No face detected in ${imageType}. Please ensure the image shows a clear, front-facing face.`);
            }

            // Check detection confidence
            if (detection.detection.score < 0.5) {
                throw new Error(`Face detection confidence too low in ${imageType}. Please capture a clearer image.`);
            }

            return detection.descriptor;

        } catch (error) {
            throw new Error(`Face detection failed for ${imageType}: ${error.message}`);
        }
    }

    compareFaceDescriptors(descriptor1, descriptor2) {
        // Calculate Euclidean distance between descriptors
        const distance = faceapi.euclideanDistance(descriptor1, descriptor2);

        // Convert distance to similarity score (0-1, where 1 is identical)
        const similarity = Math.max(0, 1 - distance);

        console.log(`Face comparison - Distance: ${distance.toFixed(3)}, Similarity: ${similarity.toFixed(3)}`);

        return similarity;
    }

    handleVerificationSuccess(userType, similarity) {
        this.verificationStatus[userType].passed = true;
        this.updateVerifyButtonState(userType, 'success');
        this.showVerificationStatus(userType, 'success',
            `Identity verified successfully! (${Math.round(similarity * 100)}% match)`);
        this.enableFormSubmission(userType);
    }

    handleVerificationFailure(userType, similarity) {
        this.verificationStatus[userType].passed = false;
        this.updateVerifyButtonState(userType, 'error');
        this.showVerificationStatus(userType, 'error',
            `Facial verification failed. Please ensure your face is clearly visible and matches your ID document, then try again. (${Math.round(similarity * 100)}% match)`);
        this.disableFormSubmission();
    }

    handleVerificationError(userType, errorMessage) {
        this.verificationStatus[userType].passed = false;
        this.updateVerifyButtonState(userType, 'error');
        this.showVerificationStatus(userType, 'error', errorMessage);
        this.disableFormSubmission();
    }

    updateVerifyButtonState(userType, state) {
        const button = userType === 'new' ? this.elements.newUserVerifyBtn : this.elements.existingUserVerifyBtn;
        if (!button) return;

        // Reset classes
        button.classList.remove('loading', 'success', 'error');

        // Update button content based on state
        switch (state) {
            case 'loading':
                button.classList.add('loading');
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Verifying...';
                button.disabled = true;
                break;

            case 'success':
                button.classList.add('success');
                button.innerHTML = '<i class="fas fa-check-circle"></i> Verified';
                button.disabled = false;
                break;

            case 'error':
                button.classList.add('error');
                button.innerHTML = '<i class="fas fa-shield-check"></i> Retry Verification';
                button.disabled = false;
                break;

            default:
                button.innerHTML = '<i class="fas fa-shield-check"></i> Verify Identity';
                button.disabled = !this.canPerformVerification(userType);
        }
    }

    showVerificationStatus(userType, type, message) {
        const statusElement = userType === 'new' ? this.elements.newUserStatus : this.elements.existingUserStatus;
        if (!statusElement) return;

        const messageElement = statusElement.querySelector('.status-message');
        const iconElement = statusElement.querySelector('.status-icon');

        messageElement.textContent = message;

        // Update icon based on type
        iconElement.innerHTML = type === 'success'
            ? '<i class="fas fa-check-circle" style="color: #28a745;"></i>'
            : '<i class="fas fa-exclamation-triangle" style="color: #dc3545;"></i>';

        statusElement.style.display = 'block';
        statusElement.className = `verification-status ${type}`;
    }

    updateVerifyButtonStates() {
        if (this.elements.newUserVerifyBtn) {
            this.elements.newUserVerifyBtn.disabled = !this.canPerformVerification('new');
        }

        if (this.elements.existingUserVerifyBtn) {
            this.elements.existingUserVerifyBtn.disabled = !this.canPerformVerification('existing');
        }
    }

    canPerformVerification(userType) {
        if (!this.modelsLoaded) return false;

        const idImageData = this.getCapturedIdImage(userType);
        const faceImageData = this.getCapturedFaceImage(userType);

        return idImageData && faceImageData;
    }

    getCapturedIdImage(userType) {
        const inputId = userType === 'new' ? 'idPhotoData' : 'existingIdPhotoData';
        const input = document.getElementById(inputId);
        return input ? input.value : null;
    }

    getCapturedFaceImage(userType) {
        const inputId = userType === 'new' ? 'facePhotoData' : 'existingFacePhotoData';
        const input = document.getElementById(inputId);
        return input ? input.value : null;
    }

    base64ToImage(base64Data) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => resolve(img);
            img.onerror = reject;
            img.src = base64Data;
        });
    }

    enableFormSubmission(userType) {
        if (userType === 'new' && this.elements.newUserSubmitBtn) {
            this.elements.newUserSubmitBtn.disabled = false;
            this.elements.newUserSubmitBtn.style.opacity = '1';
            this.elements.newUserSubmitBtn.style.cursor = 'pointer';
        } else if (userType === 'existing' && this.elements.existingUserSubmitBtn) {
            this.elements.existingUserSubmitBtn.disabled = false;
            this.elements.existingUserSubmitBtn.style.opacity = '1';
            this.elements.existingUserSubmitBtn.style.cursor = 'pointer';
        }
    }

    disableFormSubmission() {
        if (this.elements.newUserSubmitBtn) {
            this.elements.newUserSubmitBtn.disabled = true;
            this.elements.newUserSubmitBtn.style.opacity = '0.5';
            this.elements.newUserSubmitBtn.style.cursor = 'not-allowed';
        }
        if (this.elements.existingUserSubmitBtn) {
            this.elements.existingUserSubmitBtn.disabled = true;
            this.elements.existingUserSubmitBtn.style.opacity = '0.5';
            this.elements.existingUserSubmitBtn.style.cursor = 'not-allowed';
        }
    }

    handleFormSubmission(event, userType) {
        if (!this.verificationStatus[userType].passed) {
            event.preventDefault();
            this.showError('Please complete facial verification before submitting the form.');
            return false;
        }

        // Add verification status to form data
        const verificationInput = document.createElement('input');
        verificationInput.type = 'hidden';
        verificationInput.name = 'facial_verification_passed';
        verificationInput.value = 'true';
        event.target.appendChild(verificationInput);

        return true;
    }

    resetVerificationStatus(userType) {
        this.verificationStatus[userType].passed = false;
        this.verificationStatus[userType].attempted = false;
        this.disableFormSubmission();

        // Reset button state
        this.updateVerifyButtonState(userType, 'default');

        // Hide status message
        const statusElement = userType === 'new' ? this.elements.newUserStatus : this.elements.existingUserStatus;
        if (statusElement) {
            statusElement.style.display = 'none';
        }
    }

    showError(message) {
        // Create a more user-friendly error display
        const errorDiv = document.createElement('div');
        errorDiv.className = 'verification-error-message';
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-radius: 8px;
            padding: 15px;
            max-width: 400px;
            z-index: 1000;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        `;
        errorDiv.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-exclamation-triangle"></i>
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" style="margin-left: auto; background: none; border: none; font-size: 18px; cursor: pointer;">&times;</button>
            </div>
        `;

        document.body.appendChild(errorDiv);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.remove();
            }
        }, 5000);
    }
}

// Initialize the facial verification system when the page loads
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize if Face-API.js is available
    if (typeof faceapi !== 'undefined') {
        window.facialVerificationSystem = new FacialVerificationSystem();
    } else {
        console.warn('Face-API.js not loaded. Facial verification will not be available.');
    }
});
