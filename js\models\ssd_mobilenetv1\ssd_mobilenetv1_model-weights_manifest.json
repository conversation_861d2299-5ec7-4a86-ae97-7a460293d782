{"modelTopology": {"class_name": "Model", "config": {"name": "ssd_mobilenetv1", "layers": [{"name": "input_1", "class_name": "InputLayer", "config": {"batch_input_shape": [null, 416, 416, 3], "dtype": "float32", "sparse": false, "name": "input_1"}}]}}, "weightsManifest": [{"paths": ["ssd_mobilenetv1_model-shard1.bin"], "weights": [{"name": "conv1/depthwise_kernel", "shape": [3, 3, 3, 1], "dtype": "float32"}, {"name": "conv1/pointwise_kernel", "shape": [1, 1, 3, 32], "dtype": "float32"}, {"name": "conv1/bias", "shape": [32], "dtype": "float32"}]}], "format": "layers-model", "generatedBy": "TensorFlow.js tfjs-layers v1.0.0", "convertedBy": "TensorFlow.js Converter v1.0.0"}