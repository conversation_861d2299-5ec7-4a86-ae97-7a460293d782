-- Student ID Application Database Schema

-- Create the database
CREATE DATABASE IF NOT EXISTS student_id_app;
USE student_id_app;

-- Users table
CREATE TABLE IF NOT EXISTS users (
    user_id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    phone VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE,
    CONSTRAINT chk_email CHECK (email LIKE '%@%.%')
);

-- User profiles table
CREATE TABLE IF NOT EXISTS user_profiles (
    profile_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    student_id VARCHAR(20) UNIQUE,
    department VARCHAR(100),
    program VARCHAR(100),
    enrollment_year INT,
    expected_graduation INT,
    profile_image VARCHAR(255),
    emergency_contact VARCHAR(100),
    date_of_birth DATE,
    address TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    CONSTRAINT chk_enrollment_year CHECK (enrollment_year >= 2000),
    CONSTRAINT chk_graduation_year CHECK (expected_graduation >= enrollment_year)
);

-- ID card applications table
CREATE TABLE IF NOT EXISTS id_applications (
    application_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    application_type ENUM('NEW', 'RENEWAL', 'REPLACEMENT') NOT NULL,
    status ENUM('PENDING', 'APPROVED', 'REJECTED', 'COMPLETED') DEFAULT 'PENDING',
    submission_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    approval_date TIMESTAMP NULL,
    completion_date TIMESTAMP NULL,
    rejection_reason TEXT,
    notes TEXT,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Documents table
CREATE TABLE IF NOT EXISTS documents (
    document_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    document_type ENUM('GOVERNMENT_ID', 'ENROLLMENT_VERIFICATION', 'TUITION_RECEIPT', 'MEDICAL_INSURANCE', 'HOUSING_CONTRACT', 'OTHER') NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(255) NOT NULL,
    file_size INT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_verified BOOLEAN DEFAULT FALSE,
    verification_date TIMESTAMP NULL,
    verified_by INT NULL,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (verified_by) REFERENCES users(user_id) ON DELETE SET NULL
);

-- ID cards table
CREATE TABLE IF NOT EXISTS id_cards (
    card_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    application_id INT NOT NULL,
    card_number VARCHAR(50) UNIQUE NOT NULL,
    issue_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expiry_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    card_image VARCHAR(255),
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (application_id) REFERENCES id_applications(application_id) ON DELETE CASCADE
);

-- User settings table
CREATE TABLE IF NOT EXISTS user_settings (
    setting_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    theme VARCHAR(20) DEFAULT 'light',
    font_size INT DEFAULT 100,
    reduce_animations BOOLEAN DEFAULT FALSE,
    high_contrast BOOLEAN DEFAULT FALSE,
    font_family VARCHAR(50) DEFAULT 'default',
    line_spacing INT DEFAULT 150,
    screen_reader BOOLEAN DEFAULT FALSE,
    keyboard_navigation BOOLEAN DEFAULT TRUE,
    autoplay_videos BOOLEAN DEFAULT TRUE,
    image_descriptions BOOLEAN DEFAULT TRUE,
    focus_mode BOOLEAN DEFAULT FALSE,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Notification preferences table
CREATE TABLE IF NOT EXISTS notification_preferences (
    preference_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    email_notifications BOOLEAN DEFAULT TRUE,
    sms_notifications BOOLEAN DEFAULT FALSE,
    browser_notifications BOOLEAN DEFAULT TRUE,
    id_card_updates BOOLEAN DEFAULT TRUE,
    document_reminders BOOLEAN DEFAULT TRUE,
    account_activity BOOLEAN DEFAULT TRUE,
    news_updates BOOLEAN DEFAULT FALSE,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Notifications table
CREATE TABLE IF NOT EXISTS notifications (
    notification_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(100) NOT NULL,
    message TEXT NOT NULL,
    notification_type ENUM('ID_CARD', 'DOCUMENT', 'ACCOUNT', 'NEWS', 'OTHER') NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Admin users table
CREATE TABLE IF NOT EXISTS admin_users (
    admin_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    role ENUM('ADMIN', 'MODERATOR', 'SUPPORT') NOT NULL,
    department VARCHAR(100),
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Activity logs table
CREATE TABLE IF NOT EXISTS activity_logs (
    log_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    details TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL
);

-- Authentication tokens table (for "Remember Me" functionality)
CREATE TABLE IF NOT EXISTS auth_tokens (
    token_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    selector VARCHAR(255) NOT NULL UNIQUE,
    hashed_validator VARCHAR(255) NOT NULL,
    expires DATETIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Password reset tokens table
CREATE TABLE IF NOT EXISTS password_resets (
    reset_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(255) NOT NULL,
    expires_at DATETIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    used BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_profiles_student_id ON user_profiles(student_id);
CREATE INDEX idx_applications_status ON id_applications(status);
CREATE INDEX idx_applications_user ON id_applications(user_id);
CREATE INDEX idx_documents_user ON documents(user_id);
CREATE INDEX idx_documents_type ON documents(document_type);
CREATE INDEX idx_id_cards_user ON id_cards(user_id);
CREATE INDEX idx_notifications_user ON notifications(user_id);
CREATE INDEX idx_notifications_read ON notifications(is_read);
CREATE INDEX idx_activity_logs_user ON activity_logs(user_id);
CREATE INDEX idx_activity_logs_action ON activity_logs(action);
CREATE INDEX idx_auth_tokens_user ON auth_tokens(user_id);
CREATE INDEX idx_auth_tokens_selector ON auth_tokens(selector);
CREATE INDEX idx_auth_tokens_expires ON auth_tokens(expires);
CREATE INDEX idx_password_resets_user ON password_resets(user_id);
CREATE INDEX idx_password_resets_token ON password_resets(token);
CREATE INDEX idx_password_resets_expires ON password_resets(expires_at);

-- Sample data for testing
INSERT INTO users (username, password_hash, email, phone)
VALUES
('johndoe', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', '+****************'),
('janedoe', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', '+****************');

INSERT INTO user_profiles (user_id, full_name, student_id, department, program, enrollment_year, expected_graduation)
VALUES
(1, 'John Doe', 'STUD1245', 'Engineering', 'Computer Science', 2022, 2026),
(2, 'Jane Doe', 'STUD1246', 'Science', 'Biology', 2021, 2025);

INSERT INTO id_applications (user_id, application_type, status, submission_date)
VALUES
(1, 'NEW', 'PENDING', '2025-03-15 10:30:00'),
(2, 'NEW', 'APPROVED', '2025-03-10 14:45:00');

INSERT INTO notifications (user_id, title, message, notification_type)
VALUES
(1, 'ID Card Ready for Pickup', 'Your student ID card is ready for pickup at the Student Services Center.', 'ID_CARD'),
(1, 'Document Verification Required', 'Please upload your government-issued ID for verification.', 'DOCUMENT'),
(2, 'Application Approved', 'Your ID card application has been approved and is being processed.', 'ID_CARD');

-- Create a view for dashboard information
CREATE VIEW dashboard_view AS
SELECT
    u.user_id,
    u.username,
    p.full_name,
    p.student_id,
    p.department,
    a.application_id,
    a.status AS application_status,
    a.submission_date,
    COUNT(DISTINCT d.document_id) AS document_count,
    COUNT(DISTINCT n.notification_id) AS notification_count,
    COUNT(DISTINCT CASE WHEN n.is_read = 0 THEN n.notification_id END) AS unread_notification_count
FROM
    users u
LEFT JOIN
    user_profiles p ON u.user_id = p.user_id
LEFT JOIN
    id_applications a ON u.user_id = a.user_id
LEFT JOIN
    documents d ON u.user_id = d.user_id
LEFT JOIN
    notifications n ON u.user_id = n.user_id
GROUP BY
    u.user_id, a.application_id;
