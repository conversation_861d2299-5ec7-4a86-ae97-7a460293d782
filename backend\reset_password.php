<?php
/**
 * Password Reset Handler
 * 
 * This file handles the password reset process.
 */

require_once 'config.php';
require_once 'auth.php';

// Check if the form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = $_POST['email'] ?? '';
    
    // Validate email
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $_SESSION['reset_error'] = 'Please enter a valid email address';
        header('Location: reset_password.php');
        exit;
    }
    
    try {
        $db = getDbConnection();
        
        // Check if email exists
        $stmt = $db->prepare("SELECT user_id, username FROM users WHERE email = ?");
        $stmt->execute([$email]);
        $user = $stmt->fetch();
        
        if (!$user) {
            // Don't reveal that the email doesn't exist for security reasons
            $_SESSION['reset_message'] = 'If your email is registered, you will receive password reset instructions shortly.';
            header('Location: reset_password.php');
            exit;
        }
        
        // Generate a reset token
        $token = bin2hex(random_bytes(32));
        $expires = date('Y-m-d H:i:s', time() + 3600); // Token expires in 1 hour
        
        // Store the token in the database
        $stmt = $db->prepare("INSERT INTO password_resets (user_id, token, expires_at) VALUES (?, ?, ?)");
        $stmt->execute([$user['user_id'], $token, $expires]);
        
        // In a real application, send an email with the reset link
        // For now, just show a success message
        $_SESSION['reset_message'] = 'If your email is registered, you will receive password reset instructions shortly.';
        
        // Log the password reset request
        logActivity($user['user_id'], 'PASSWORD_RESET_REQUEST', 'Password reset requested');
        
        header('Location: reset_password.php');
        exit;
    } catch (PDOException $e) {
        error_log("Password Reset Error: " . $e->getMessage());
        $_SESSION['reset_error'] = 'An error occurred. Please try again later.';
        header('Location: reset_password.php');
        exit;
    }
}

// Display the password reset form
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - Student ID Application</title>
    <link rel="stylesheet" href="../css/styles.css">
</head>
<body>
    <div class="container">
        <h1>Reset Password</h1>
        <p class="subtitle">Enter your email to receive password reset instructions</p>
        
        <?php if (isset($_SESSION['reset_error'])): ?>
            <div class="error-message">
                <?php echo $_SESSION['reset_error']; unset($_SESSION['reset_error']); ?>
            </div>
        <?php endif; ?>
        
        <?php if (isset($_SESSION['reset_message'])): ?>
            <div class="success-message">
                <?php echo $_SESSION['reset_message']; unset($_SESSION['reset_message']); ?>
            </div>
        <?php endif; ?>
        
        <form action="reset_password.php" method="post">
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" placeholder="Enter your email" required>
            </div>
            
            <button type="submit" class="btn-primary">Reset Password</button>
        </form>
        
        <p class="switch-form"><a href="../index.html">Back to Login</a></p>
    </div>
    
    <footer>
        <p>We respect your privacy. All information is encrypted and secure.</p>
    </footer>
</body>
</html>
