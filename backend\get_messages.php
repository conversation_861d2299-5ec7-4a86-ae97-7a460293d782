<?php
/**
 * Get Messages Handler
 *
 * This file returns session messages as <PERSON><PERSON><PERSON> for the frontend to display.
 */

require_once 'config.php';

// Set content type to JSON
header('Content-Type: application/json');

// Initialize response array
$response = [];

// Check for login error message
if (isset($_SESSION['login_error'])) {
    $response['error'] = $_SESSION['login_error'];
    unset($_SESSION['login_error']);
}

// Check for signup error message
if (isset($_SESSION['signup_error'])) {
    $response['error'] = $_SESSION['signup_error'];
    unset($_SESSION['signup_error']);
}

// Check for success message
if (isset($_SESSION['login_message'])) {
    $response['message'] = $_SESSION['login_message'];
    unset($_SESSION['login_message']);
}

// Debug session data
error_log("Session data: " . print_r($_SESSION, true));
error_log("Response data: " . print_r($response, true));

// Return the response as <PERSON><PERSON><PERSON>
echo json_encode($response);
