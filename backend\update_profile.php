<?php
/**
 * Update User Profile
 *
 * This file handles updating user profile information including profile image.
 */

require_once 'config.php';
require_once 'auth.php';

// Redirect to login if not authenticated
if (!isLoggedIn()) {
    header('Location: ../index.php');
    exit;
}

// Get user ID from session
$userId = $_SESSION['user_id'];

// Check if the form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    $submittedToken = $_POST['csrf_token'] ?? '';
    if (!hash_equals($_SESSION['csrf_token'], $submittedToken)) {
        $_SESSION['profile_error'] = 'Invalid form submission. Please try again.';
        header('Location: ../html/profile.php');
        exit;
    }

    // Get form data
    $fullName = $_POST['full_name'] ?? '';

    // Validate required fields
    if (empty($fullName)) {
        $_SESSION['profile_error'] = 'Full name is required.';
        header('Location: ../html/profile.php');
        exit;
    }

    try {
        $db = getDbConnection();
        $profileImagePath = null;

        // Handle profile image upload if a file was submitted
        if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] === UPLOAD_ERR_OK) {
            $file = $_FILES['profile_image'];

            // Validate file size
            if ($file['size'] > MAX_FILE_SIZE) {
                $_SESSION['profile_error'] = 'File size exceeds the limit of 2MB.';
                header('Location: ../html/profile.php');
                exit;
            }

            // Validate file type
            $fileType = $file['type'];
            if ($fileType !== 'image/jpeg' && $fileType !== 'image/png') {
                $_SESSION['profile_error'] = 'Only JPG and PNG image files are allowed.';
                header('Location: ../html/profile.php');
                exit;
            }

            // Generate unique filename
            $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            $newFilename = 'profile_' . $userId . '_' . uniqid() . '.' . $fileExtension;
            $uploadPath = UPLOAD_DIR . $newFilename;

            // Create uploads directory if it doesn't exist
            if (!file_exists(UPLOAD_DIR)) {
                mkdir(UPLOAD_DIR, 0755, true);
            }

            // Move uploaded file
            if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
                // Store the relative path in the database for easier access in HTML
                $profileImagePath = '../uploads/' . $newFilename;
            } else {
                $_SESSION['profile_error'] = 'Failed to upload image. Please try again.';
                header('Location: ../html/profile.php');
                exit;
            }
        }

        // Update user profile
        if ($profileImagePath) {
            $stmt = $db->prepare("UPDATE user_profiles SET full_name = ?, profile_image = ? WHERE user_id = ?");
            $stmt->execute([$fullName, $profileImagePath, $userId]);
        } else {
            $stmt = $db->prepare("UPDATE user_profiles SET full_name = ? WHERE user_id = ?");
            $stmt->execute([$fullName, $userId]);
        }

        // Update session variable
        $_SESSION['full_name'] = $fullName;

        // Log the profile update
        logActivity($userId, 'PROFILE_UPDATE', 'User updated profile information');

        // Set success message
        $_SESSION['profile_message'] = 'Profile updated successfully.';

        // Redirect back to profile page
        header('Location: ../html/profile.php');
        exit;

    } catch (PDOException $e) {
        error_log("Profile Update Error: " . $e->getMessage());
        $_SESSION['profile_error'] = 'An error occurred while updating your profile. Please try again.';
        header('Location: ../html/profile.php');
        exit;
    }
} else {
    // Not a POST request, redirect to profile page
    header('Location: ../html/profile.php');
    exit;
}
