-- Migration script to add id_card_photo column to id_applications table
-- This separates the ID card photo from the profile picture

USE student_id_app;

-- Add id_card_photo column to id_applications table
ALTER TABLE id_applications 
ADD COLUMN id_card_photo VARCHAR(255) NULL 
AFTER status;

-- Add index for better performance
CREATE INDEX idx_id_applications_photo ON id_applications(id_card_photo);

-- Update existing applications to have NULL id_card_photo (they will need to resubmit with photo)
-- This ensures separation between profile pictures and ID card photos
UPDATE id_applications 
SET id_card_photo = NULL 
WHERE id_card_photo IS NULL;

-- Add comment to document the purpose of this column
ALTER TABLE id_applications 
MODIFY COLUMN id_card_photo VARCHAR(255) NULL 
COMMENT 'Path to ID card photo uploaded specifically for this application (separate from profile picture)';
