/**
 * Facial Recognition System for Student ID Application
 *
 * This module handles:
 * 1. National ID card photo capture
 * 2. Face detection and descriptor extraction from ID
 * 3. Live facial verification during form submission
 * 4. Face matching with confidence scoring
 */

class FacialRecognitionSystem {
    constructor() {
        this.isModelsLoaded = false;
        this.idFaceDescriptor = null;
        this.currentStream = null;
        this.verificationStream = null;
        this.matchingThreshold = 0.6;
        this.isVerificationActive = false;

        // DOM elements
        this.elements = {
            // Camera interface
            startCameraBtn: document.getElementById('startCameraBtn'),
            cameraInterface: document.getElementById('cameraInterface'),
            cameraVideo: document.getElementById('cameraVideo'),
            cameraCanvas: document.getElementById('cameraCanvas'),
            captureIdBtn: document.getElementById('captureIdBtn'),
            cancelCameraBtn: document.getElementById('cancelCameraBtn'),
            retakePhotoBtn: document.getElementById('retakePhotoBtn'),

            // Photo preview
            photoCaptureContainer: document.getElementById('photoCaptureContainer'),
            idCardPhotoPreview: document.getElementById('idCardPhotoPreview'),
            idCardPreviewPhoto: document.getElementById('idCardPreviewPhoto'),
            facialRecognitionStatus: document.getElementById('facialRecognitionStatus'),

            // Verification modal
            facialVerificationModal: document.getElementById('facialVerificationModal'),
            verificationVideo: document.getElementById('verificationVideo'),
            verificationCanvas: document.getElementById('verificationCanvas'),
            faceDetectionOverlay: document.getElementById('faceDetectionOverlay'),
            verificationStatus: document.getElementById('verificationStatus'),
            confidenceBar: document.getElementById('confidenceBar'),

            // Modal controls
            cancelVerificationBtn: document.getElementById('cancelVerificationBtn'),
            skipVerificationBtn: document.getElementById('skipVerificationBtn'),
            retryVerificationBtn: document.getElementById('retryVerificationBtn'),

            // Form elements
            idCardForm: document.getElementById('idCardForm'),
            capturedPhotoData: document.getElementById('capturedPhotoData'),
            facialDescriptor: document.getElementById('facialDescriptor')
        };

        this.init();
    }

    async init() {
        try {
            await this.loadFaceAPIModels();
            this.setupEventListeners();
            console.log('Facial Recognition System initialized successfully');
        } catch (error) {
            console.error('Failed to initialize Facial Recognition System:', error);
            this.showError('Failed to load facial recognition models. Please refresh the page.');
        }
    }

    async loadFaceAPIModels() {
        const modelUrl = 'https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/models';

        try {
            await Promise.all([
                faceapi.nets.ssdMobilenetv1.loadFromUri(modelUrl),
                faceapi.nets.faceLandmark68Net.loadFromUri(modelUrl),
                faceapi.nets.faceRecognitionNet.loadFromUri(modelUrl),
                faceapi.nets.faceExpressionNet.loadFromUri(modelUrl),
                faceapi.nets.ageGenderNet.loadFromUri(modelUrl)
            ]);

            this.isModelsLoaded = true;
            console.log('Face-API models loaded successfully');
        } catch (error) {
            console.error('Error loading Face-API models:', error);
            throw new Error('Failed to load facial recognition models');
        }
    }

    setupEventListeners() {
        // Camera controls
        this.elements.startCameraBtn?.addEventListener('click', () => this.startCamera());
        this.elements.captureIdBtn?.addEventListener('click', () => this.captureIdPhoto());
        this.elements.cancelCameraBtn?.addEventListener('click', () => this.stopCamera());
        this.elements.retakePhotoBtn?.addEventListener('click', () => this.retakePhoto());

        // Verification modal controls
        this.elements.cancelVerificationBtn?.addEventListener('click', () => this.cancelVerification());
        this.elements.skipVerificationBtn?.addEventListener('click', () => this.skipVerification());
        this.elements.retryVerificationBtn?.addEventListener('click', () => this.retryVerification());

        // Form submission intercept
        this.elements.idCardForm?.addEventListener('submit', (e) => this.handleFormSubmission(e));
    }

    async startCamera() {
        try {
            // Request camera permission with appropriate constraints
            const constraints = {
                video: {
                    width: { ideal: 1280 },
                    height: { ideal: 720 },
                    facingMode: this.isMobileDevice() ? 'environment' : 'user'
                }
            };

            this.currentStream = await navigator.mediaDevices.getUserMedia(constraints);
            this.elements.cameraVideo.srcObject = this.currentStream;

            // Show camera interface
            this.elements.photoCaptureContainer.style.display = 'none';
            this.elements.cameraInterface.style.display = 'block';

            // Update instructions based on device
            this.updateCameraInstructions();

        } catch (error) {
            console.error('Error accessing camera:', error);
            this.showError('Camera access denied. Please allow camera permissions and try again.');
        }
    }

    updateCameraInstructions() {
        const title = document.getElementById('cameraTitle');
        const instructions = document.getElementById('cameraInstructions');

        if (this.isMobileDevice()) {
            title.textContent = 'Position your National Identity Card in the frame';
            instructions.textContent = 'Use the rear camera for better document capture quality';
        } else {
            title.textContent = 'Position your National Identity Card in the frame';
            instructions.textContent = 'Ensure good lighting and that the ID card is clearly visible';
        }
    }

    async captureIdPhoto() {
        if (!this.currentStream) return;

        try {
            // Capture photo from video stream
            const canvas = this.elements.cameraCanvas;
            const video = this.elements.cameraVideo;

            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;

            const ctx = canvas.getContext('2d');
            ctx.drawImage(video, 0, 0);

            // Convert to blob and extract face descriptor
            const photoBlob = await this.canvasToBlob(canvas);
            const photoDataUrl = canvas.toDataURL('image/jpeg', 0.8);

            // Extract facial descriptor from captured ID
            await this.extractFacialDescriptor(canvas);

            // Update UI
            this.displayCapturedPhoto(photoDataUrl);
            this.elements.capturedPhotoData.value = photoDataUrl;

            // Stop camera and show success
            this.stopCamera();
            this.showFacialRecognitionReady();

        } catch (error) {
            console.error('Error capturing photo:', error);
            this.showError('Failed to capture photo. Please try again.');
        }
    }

    async extractFacialDescriptor(canvas) {
        if (!this.isModelsLoaded) {
            throw new Error('Face-API models not loaded');
        }

        try {
            const detections = await faceapi
                .detectAllFaces(canvas)
                .withFaceLandmarks()
                .withFaceDescriptors();

            if (detections.length === 0) {
                throw new Error('No face detected in the ID document');
            }

            if (detections.length > 1) {
                console.warn('Multiple faces detected, using the first one');
            }

            // Store the face descriptor for later comparison
            this.idFaceDescriptor = detections[0].descriptor;
            this.elements.facialDescriptor.value = JSON.stringify(Array.from(this.idFaceDescriptor));

            console.log('Facial descriptor extracted successfully');

        } catch (error) {
            console.error('Error extracting facial descriptor:', error);
            throw new Error('Failed to detect face in ID document. Please ensure the photo shows a clear face.');
        }
    }

    displayCapturedPhoto(photoDataUrl) {
        // Update photo preview
        this.elements.idCardPhotoPreview.innerHTML = `<img src="${photoDataUrl}" alt="Captured ID" style="width: 100%; height: 100%; object-fit: cover; border-radius: 5px;">`;

        // Update ID card preview
        this.elements.idCardPreviewPhoto.innerHTML = `<img src="${photoDataUrl}" alt="ID Photo" style="width: 100%; height: 100%; object-fit: cover;">`;

        // Show retake button
        this.elements.retakePhotoBtn.style.display = 'inline-flex';
    }

    showFacialRecognitionReady() {
        this.elements.facialRecognitionStatus.style.display = 'flex';
    }

    stopCamera() {
        if (this.currentStream) {
            this.currentStream.getTracks().forEach(track => track.stop());
            this.currentStream = null;
        }

        this.elements.cameraInterface.style.display = 'none';
        this.elements.photoCaptureContainer.style.display = 'block';
    }

    retakePhoto() {
        // Reset captured data
        this.idFaceDescriptor = null;
        this.elements.capturedPhotoData.value = '';
        this.elements.facialDescriptor.value = '';

        // Reset UI
        this.elements.idCardPhotoPreview.innerHTML = '<i class="fas fa-id-card"></i><p>Capture your National ID document</p>';
        this.elements.idCardPreviewPhoto.innerHTML = '<i class="fas fa-user"></i><p class="photo-placeholder">Photo will appear here</p>';
        this.elements.retakePhotoBtn.style.display = 'none';
        this.elements.facialRecognitionStatus.style.display = 'none';

        // Start camera again
        this.startCamera();
    }

    async handleFormSubmission(event) {
        // Check if facial recognition is required and available
        if (!this.idFaceDescriptor) {
            event.preventDefault();
            this.showError('Please capture your ID document first for facial verification.');
            return;
        }

        // Prevent default submission and start facial verification
        event.preventDefault();
        await this.startFacialVerification();
    }

    isMobileDevice() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    canvasToBlob(canvas) {
        return new Promise(resolve => {
            canvas.toBlob(resolve, 'image/jpeg', 0.8);
        });
    }

    showError(message) {
        alert(message); // Simple error display - can be enhanced with custom modal
    }

    showSuccess(message) {
        alert(message); // Simple success display - can be enhanced with custom modal
    }

    async startFacialVerification() {
        try {
            // Show verification modal
            this.elements.facialVerificationModal.style.display = 'flex';
            this.isVerificationActive = true;

            // Request camera for verification (front-facing for selfie)
            const constraints = {
                video: {
                    width: { ideal: 640 },
                    height: { ideal: 480 },
                    facingMode: 'user' // Always use front camera for verification
                }
            };

            this.verificationStream = await navigator.mediaDevices.getUserMedia(constraints);
            this.elements.verificationVideo.srcObject = this.verificationStream;

            // Start real-time face detection
            this.startRealTimeFaceDetection();

        } catch (error) {
            console.error('Error starting facial verification:', error);
            this.showError('Camera access denied for verification. Please allow camera permissions.');
            this.cancelVerification();
        }
    }

    async startRealTimeFaceDetection() {
        if (!this.isVerificationActive || !this.verificationStream) return;

        try {
            const video = this.elements.verificationVideo;
            const canvas = this.elements.verificationCanvas;
            const overlay = this.elements.faceDetectionOverlay;

            // Set canvas dimensions to match video
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;

            const detections = await faceapi
                .detectAllFaces(video)
                .withFaceLandmarks()
                .withFaceDescriptors()
                .withAgeAndGender()
                .withFaceExpressions();

            // Clear previous overlays
            overlay.innerHTML = '';

            if (detections.length > 0) {
                const detection = detections[0]; // Use first detected face

                // Calculate face matching confidence
                const distance = faceapi.euclideanDistance(this.idFaceDescriptor, detection.descriptor);
                const confidence = Math.max(0, 1 - distance);

                // Update confidence meter
                this.updateConfidenceMeter(confidence);

                // Draw face detection box
                this.drawFaceDetectionBox(detection, confidence >= this.matchingThreshold);

                // Update status message
                this.updateVerificationStatus(confidence, detection);

                // Check if verification is successful
                if (confidence >= this.matchingThreshold) {
                    setTimeout(() => {
                        if (this.isVerificationActive) {
                            this.completeVerification(true, confidence);
                        }
                    }, 2000); // Wait 2 seconds for stable detection
                    return;
                }
            } else {
                // No face detected
                this.updateVerificationStatus(0, null);
                this.updateConfidenceMeter(0);
            }

            // Continue detection loop
            if (this.isVerificationActive) {
                requestAnimationFrame(() => this.startRealTimeFaceDetection());
            }

        } catch (error) {
            console.error('Error in face detection:', error);
            if (this.isVerificationActive) {
                requestAnimationFrame(() => this.startRealTimeFaceDetection());
            }
        }
    }

    updateConfidenceMeter(confidence) {
        const percentage = Math.round(confidence * 100);
        this.elements.confidenceBar.style.width = `${percentage}%`;
    }

    drawFaceDetectionBox(detection, isMatched) {
        const overlay = this.elements.faceDetectionOverlay;
        const video = this.elements.verificationVideo;

        // Calculate scaling factors
        const scaleX = video.offsetWidth / video.videoWidth;
        const scaleY = video.offsetHeight / video.videoHeight;

        // Create face box element
        const faceBox = document.createElement('div');
        faceBox.className = `face-box ${isMatched ? 'matched' : 'unmatched'}`;

        const box = detection.detection.box;
        faceBox.style.left = `${box.x * scaleX}px`;
        faceBox.style.top = `${box.y * scaleY}px`;
        faceBox.style.width = `${box.width * scaleX}px`;
        faceBox.style.height = `${box.height * scaleY}px`;

        // Add label
        const label = document.createElement('div');
        label.className = `face-label ${isMatched ? 'matched' : 'unmatched'}`;
        label.textContent = isMatched ? 'MATCH' : 'NO MATCH';
        faceBox.appendChild(label);

        overlay.appendChild(faceBox);
    }

    updateVerificationStatus(confidence, detection) {
        const statusMessage = document.querySelector('.status-message');
        const percentage = Math.round(confidence * 100);

        if (!detection) {
            statusMessage.textContent = 'Position your face in the camera';
        } else if (confidence >= this.matchingThreshold) {
            statusMessage.textContent = `Identity Verified! (${percentage}% match)`;
        } else {
            statusMessage.textContent = `Verifying... (${percentage}% match)`;
        }
    }

    completeVerification(success, confidence) {
        this.isVerificationActive = false;

        if (success) {
            this.showSuccess(`Identity verified successfully! (${Math.round(confidence * 100)}% match)`);
            this.stopVerificationCamera();
            this.elements.facialVerificationModal.style.display = 'none';

            // Submit the form
            this.submitFormWithVerification();
        } else {
            this.showError('Facial verification failed. Please try again.');
            this.elements.retryVerificationBtn.style.display = 'inline-flex';
        }
    }

    submitFormWithVerification() {
        // Add verification success flag
        const verificationInput = document.createElement('input');
        verificationInput.type = 'hidden';
        verificationInput.name = 'facial_verification_passed';
        verificationInput.value = 'true';
        this.elements.idCardForm.appendChild(verificationInput);

        // Submit the form
        this.elements.idCardForm.submit();
    }

    cancelVerification() {
        this.isVerificationActive = false;
        this.stopVerificationCamera();
        this.elements.facialVerificationModal.style.display = 'none';
    }

    skipVerification() {
        this.isVerificationActive = false;
        this.stopVerificationCamera();
        this.elements.facialVerificationModal.style.display = 'none';

        // Submit form without verification
        const skipInput = document.createElement('input');
        skipInput.type = 'hidden';
        skipInput.name = 'facial_verification_skipped';
        skipInput.value = 'true';
        this.elements.idCardForm.appendChild(skipInput);

        this.elements.idCardForm.submit();
    }

    retryVerification() {
        this.elements.retryVerificationBtn.style.display = 'none';
        this.isVerificationActive = true;
        this.startRealTimeFaceDetection();
    }

    stopVerificationCamera() {
        if (this.verificationStream) {
            this.verificationStream.getTracks().forEach(track => track.stop());
            this.verificationStream = null;
        }
    }
}

// Initialize the facial recognition system when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize if we're on the create ID page
    if (document.getElementById('startCameraBtn')) {
        window.facialRecognitionSystem = new FacialRecognitionSystem();
    }
});
