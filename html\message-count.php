<?php
// Message count helper for all pages
// This file should be included after user authentication

try {
    // Get message count for login notifications if not already loaded
    if (!isset($messageCount)) {
        require_once '../backend/login_notifications.php';
        $messageCount = getUnreadMessageCount($userId);
    }
} catch (Exception $e) {
    error_log("Message Count Error: " . $e->getMessage());
    $messageCount = 0;
}
?>
