{"modelTopology": {"class_name": "Model", "config": {"name": "tiny_face_detector", "layers": [{"name": "input_1", "class_name": "InputLayer", "config": {"batch_input_shape": [null, null, null, 3], "dtype": "float32", "sparse": false, "name": "input_1"}}]}}, "weightsManifest": [{"paths": ["tiny_face_detector_model-shard1.bin"], "weights": [{"name": "conv2d_1/kernel", "shape": [3, 3, 3, 16], "dtype": "float32"}, {"name": "conv2d_1/bias", "shape": [16], "dtype": "float32"}]}], "format": "layers-model", "generatedBy": "TensorFlow.js tfjs-layers v1.0.0", "convertedBy": "TensorFlow.js Converter v1.0.0"}