{"modelTopology": {"class_name": "Model", "config": {"name": "face_expression", "layers": [{"name": "input_1", "class_name": "InputLayer", "config": {"batch_input_shape": [null, 48, 48, 1], "dtype": "float32", "sparse": false, "name": "input_1"}}]}}, "weightsManifest": [{"paths": ["face_expression_model-shard1.bin"], "weights": [{"name": "conv2d_1/kernel", "shape": [3, 3, 1, 64], "dtype": "float32"}, {"name": "conv2d_1/bias", "shape": [64], "dtype": "float32"}, {"name": "dense_1/kernel", "shape": [1024, 7], "dtype": "float32"}, {"name": "dense_1/bias", "shape": [7], "dtype": "float32"}]}], "format": "layers-model", "generatedBy": "TensorFlow.js tfjs-layers v1.0.0", "convertedBy": "TensorFlow.js Converter v1.0.0"}