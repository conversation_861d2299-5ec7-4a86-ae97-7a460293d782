document.addEventListener('DOMContentLoaded', function() {
    // Update radio button to match saved theme
    const savedTheme = window.ThemeLoader.getCurrentTheme();
    const savedThemeRadio = document.querySelector(`input[name="theme"][value="${savedTheme}"]`);
    if (savedThemeRadio) {
        savedThemeRadio.checked = true;
    }

    // Theme selection
    const themeOptions = document.querySelectorAll('input[name="theme"]');

    themeOptions.forEach(option => {
        option.addEventListener('change', function() {
            const selectedTheme = this.value;
            window.ThemeLoader.setTheme(selectedTheme);
        });
    });

    // Handle form submission to apply theme immediately
    const displayForm = document.getElementById('displayPreferencesForm');
    if (displayForm) {
        displayForm.addEventListener('submit', function(e) {
            // Get the selected theme before form submission
            const selectedThemeRadio = document.querySelector('input[name="theme"]:checked');
            if (selectedThemeRadio) {
                const selectedTheme = selectedThemeRadio.value;
                window.ThemeLoader.setTheme(selectedTheme);
            }
        });
    }

    // Font size slider
    const fontSizeSlider = document.getElementById('fontSize');
    const fontSizeValue = document.querySelector('.range-slider .range-value');

    if (fontSizeSlider && fontSizeValue) {
        fontSizeSlider.addEventListener('input', function() {
            const value = this.value;
            fontSizeValue.textContent = `${value}%`;

            // In a real application, this would adjust the font size
            // For demonstration, let's actually change the font size of the page
            document.documentElement.style.fontSize = `${value}%`;
        });
    }



    // Toggle switches
    const toggleSwitches = document.querySelectorAll('.toggle-switch input[type="checkbox"]');

    toggleSwitches.forEach(toggle => {
        toggle.addEventListener('change', function() {
            const toggleId = this.id;
            const isChecked = this.checked;

            // Handle specific toggles
            switch (toggleId) {
                case 'reduceAnimations':
                    if (isChecked) {
                        document.body.classList.add('reduce-animations');
                    } else {
                        document.body.classList.remove('reduce-animations');
                    }
                    break;

                case 'highContrast':
                    if (isChecked) {
                        document.body.classList.add('high-contrast');
                    } else {
                        document.body.classList.remove('high-contrast');
                    }
                    break;



                default:
                    console.log(`Toggle ${toggleId}: ${isChecked ? 'enabled' : 'disabled'}`);
            }
        });
    });


});
