document.addEventListener('DOMContentLoaded', function() {
    // Theme selection
    const themeOptions = document.querySelectorAll('input[name="theme"]');
    
    themeOptions.forEach(option => {
        option.addEventListener('change', function() {
            const selectedTheme = this.value;
            
            // In a real application, this would apply the selected theme
            // For now, just log the selection
            console.log(`Theme changed to: ${selectedTheme}`);
            
            // Show a message to the user
            alert(`Theme changed to ${selectedTheme}. In a real application, this would update the site's appearance.`);
        });
    });
    
    // Font size slider
    const fontSizeSlider = document.getElementById('fontSize');
    const fontSizeValue = document.querySelector('.range-slider .range-value');
    
    if (fontSizeSlider && fontSizeValue) {
        fontSizeSlider.addEventListener('input', function() {
            const value = this.value;
            fontSizeValue.textContent = `${value}%`;
            
            // In a real application, this would adjust the font size
            // For demonstration, let's actually change the font size of the page
            document.documentElement.style.fontSize = `${value}%`;
        });
    }
    
    // Line spacing slider
    const lineSpacingSlider = document.getElementById('lineSpacing');
    const lineSpacingValue = lineSpacingSlider ? lineSpacingSlider.nextElementSibling : null;
    
    if (lineSpacingSlider && lineSpacingValue) {
        lineSpacingSlider.addEventListener('input', function() {
            const value = this.value;
            lineSpacingValue.textContent = `${value}%`;
            
            // In a real application, this would adjust the line spacing
            // For demonstration, let's actually change the line height of paragraphs
            const paragraphs = document.querySelectorAll('p');
            paragraphs.forEach(p => {
                p.style.lineHeight = `${value}%`;
            });
        });
    }
    
    // Font family selection
    const fontFamilySelect = document.getElementById('fontFamily');
    
    if (fontFamilySelect) {
        fontFamilySelect.addEventListener('change', function() {
            const selectedFont = this.value;
            
            // Map the selected value to an actual font family
            let fontFamily;
            switch (selectedFont) {
                case 'default':
                    fontFamily = 'Arial, sans-serif';
                    break;
                case 'dyslexic':
                    fontFamily = 'OpenDyslexic, Arial, sans-serif';
                    break;
                case 'serif':
                    fontFamily = 'Georgia, serif';
                    break;
                case 'monospace':
                    fontFamily = 'Consolas, monospace';
                    break;
                default:
                    fontFamily = 'Arial, sans-serif';
            }
            
            // In a real application, this would apply the selected font
            // For demonstration, let's actually change the font family
            document.body.style.fontFamily = fontFamily;
        });
    }
    
    // Toggle switches
    const toggleSwitches = document.querySelectorAll('.toggle-switch input[type="checkbox"]');
    
    toggleSwitches.forEach(toggle => {
        toggle.addEventListener('change', function() {
            const toggleId = this.id;
            const isChecked = this.checked;
            
            // Handle specific toggles
            switch (toggleId) {
                case 'reduceAnimations':
                    if (isChecked) {
                        document.body.classList.add('reduce-animations');
                    } else {
                        document.body.classList.remove('reduce-animations');
                    }
                    break;
                    
                case 'highContrast':
                    if (isChecked) {
                        document.body.classList.add('high-contrast');
                    } else {
                        document.body.classList.remove('high-contrast');
                    }
                    break;
                    
                case 'screenReader':
                    // In a real application, this would optimize for screen readers
                    console.log(`Screen reader support: ${isChecked ? 'enabled' : 'disabled'}`);
                    break;
                    
                case 'keyboardNav':
                    // In a real application, this would enhance keyboard navigation
                    console.log(`Enhanced keyboard navigation: ${isChecked ? 'enabled' : 'disabled'}`);
                    break;
                    
                case 'focusMode':
                    if (isChecked) {
                        document.body.classList.add('focus-mode');
                    } else {
                        document.body.classList.remove('focus-mode');
                    }
                    break;
                    
                default:
                    console.log(`Toggle ${toggleId}: ${isChecked ? 'enabled' : 'disabled'}`);
            }
        });
    });
    
    // Save all preferences button
    const saveButton = document.querySelector('.save-button');
    
    if (saveButton) {
        saveButton.addEventListener('click', function(e) {
            e.preventDefault();
            
            // In a real application, this would save all preferences to the server
            // For now, just show an alert
            alert('All accessibility preferences saved successfully!');
        });
    }
});
