<?php
/**
 * Update Accessibility Settings
 * 
 * This file handles updating user accessibility settings.
 */

require_once 'config.php';
require_once 'auth.php';

// Redirect to login if not authenticated
if (!isLoggedIn()) {
    header('Location: ../index.php');
    exit;
}

// Get user ID from session
$userId = $_SESSION['user_id'];

// Check if the form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    $submittedToken = $_POST['csrf_token'] ?? '';
    if (!hash_equals($_SESSION['csrf_token'], $submittedToken)) {
        $_SESSION['settings_error'] = 'Invalid form submission. Please try again.';
        header('Location: ../html/accessibility.php');
        exit;
    }
    
    $settingsType = $_POST['settings_type'] ?? '';
    
    try {
        $db = getDbConnection();
        
        // Check if user already has accessibility settings
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM user_accessibility WHERE user_id = ?");
        $stmt->execute([$userId]);
        $result = $stmt->fetch();
        $hasSettings = $result['count'] > 0;
        
        // Process based on settings type
        switch ($settingsType) {
            case 'display':
                $theme = $_POST['theme'] ?? 'light';
                $fontSize = (int)($_POST['font_size'] ?? 100);
                $reduceAnimations = isset($_POST['reduce_animations']) ? 1 : 0;
                $highContrast = isset($_POST['high_contrast']) ? 1 : 0;
                
                if ($hasSettings) {
                    $stmt = $db->prepare("
                        UPDATE user_accessibility 
                        SET theme = ?, font_size = ?, reduce_animations = ?, high_contrast = ?, updated_at = NOW()
                        WHERE user_id = ?
                    ");
                    $stmt->execute([$theme, $fontSize, $reduceAnimations, $highContrast, $userId]);
                } else {
                    $stmt = $db->prepare("
                        INSERT INTO user_accessibility 
                        (user_id, theme, font_size, reduce_animations, high_contrast)
                        VALUES (?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([$userId, $theme, $fontSize, $reduceAnimations, $highContrast]);
                }
                break;
                
            case 'reading':
                $fontFamily = $_POST['font_family'] ?? 'default';
                $lineSpacing = (int)($_POST['line_spacing'] ?? 150);
                $screenReader = isset($_POST['screen_reader']) ? 1 : 0;
                $keyboardNav = isset($_POST['keyboard_nav']) ? 1 : 0;
                
                if ($hasSettings) {
                    $stmt = $db->prepare("
                        UPDATE user_accessibility 
                        SET font_family = ?, line_spacing = ?, screen_reader = ?, keyboard_nav = ?, updated_at = NOW()
                        WHERE user_id = ?
                    ");
                    $stmt->execute([$fontFamily, $lineSpacing, $screenReader, $keyboardNav, $userId]);
                } else {
                    $stmt = $db->prepare("
                        INSERT INTO user_accessibility 
                        (user_id, font_family, line_spacing, screen_reader, keyboard_nav)
                        VALUES (?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([$userId, $fontFamily, $lineSpacing, $screenReader, $keyboardNav]);
                }
                break;
                
            case 'content':
                $autoplayVideos = isset($_POST['autoplay_videos']) ? 1 : 0;
                $imageDescriptions = isset($_POST['image_descriptions']) ? 1 : 0;
                $focusMode = isset($_POST['focus_mode']) ? 1 : 0;
                
                if ($hasSettings) {
                    $stmt = $db->prepare("
                        UPDATE user_accessibility 
                        SET autoplay_videos = ?, image_descriptions = ?, focus_mode = ?, updated_at = NOW()
                        WHERE user_id = ?
                    ");
                    $stmt->execute([$autoplayVideos, $imageDescriptions, $focusMode, $userId]);
                } else {
                    $stmt = $db->prepare("
                        INSERT INTO user_accessibility 
                        (user_id, autoplay_videos, image_descriptions, focus_mode)
                        VALUES (?, ?, ?, ?)
                    ");
                    $stmt->execute([$userId, $autoplayVideos, $imageDescriptions, $focusMode]);
                }
                break;
                
            default:
                $_SESSION['settings_error'] = 'Invalid settings type.';
                header('Location: ../html/accessibility.php');
                exit;
        }
        
        // Log the activity
        logActivity($userId, 'UPDATE_ACCESSIBILITY', 'Updated accessibility settings: ' . $settingsType);
        
        // Set success message
        $_SESSION['settings_message'] = 'Accessibility settings updated successfully.';
        
        // Redirect back to accessibility page
        header('Location: ../html/accessibility.php');
        exit;
        
    } catch (PDOException $e) {
        error_log("Accessibility Settings Error: " . $e->getMessage());
        $_SESSION['settings_error'] = 'An error occurred while updating your settings. Please try again.';
        header('Location: ../html/accessibility.php');
        exit;
    }
} else {
    // Not a POST request, redirect to accessibility page
    header('Location: ../html/accessibility.php');
    exit;
}
