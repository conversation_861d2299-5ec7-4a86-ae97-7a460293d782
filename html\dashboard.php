<?php
/**
 * Dashboard Page
 *
 * This is the main dashboard page for authenticated users.
 */

require_once '../backend/config.php';
require_once '../backend/auth.php';

// Redirect to login if not authenticated
if (!isLoggedIn()) {
    header('Location: ../index.html');
    exit;
}

// Get user data from session
$userId = $_SESSION['user_id'];
$username = $_SESSION['username'];
$fullName = $_SESSION['full_name'];
$studentId = $_SESSION['student_id'];

// Get user's application status
try {
    $db = getDbConnection();

    // Get the latest application
    $stmt = $db->prepare("
        SELECT application_id, application_type, status, submission_date
        FROM id_applications
        WHERE user_id = ?
        ORDER BY submission_date DESC
        LIMIT 1
    ");
    $stmt->execute([$userId]);
    $application = $stmt->fetch();

    // Get recent notifications
    $stmt = $db->prepare("
        SELECT notification_id, title, message, notification_type, created_at
        FROM notifications
        WHERE user_id = ?
        <PERSON><PERSON><PERSON> BY created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$userId]);
    $notifications = $stmt->fetchAll();

    // Get unread notification count
    $stmt = $db->prepare("
        SELECT COUNT(*) as count
        FROM notifications
        WHERE user_id = ? AND is_read = 0
    ");
    $stmt->execute([$userId]);
    $unreadCount = $stmt->fetch()['count'];

    // Get user theme settings
    $stmt = $db->prepare("
        SELECT theme FROM user_settings
        WHERE user_id = ?
    ");
    $stmt->execute([$userId]);
    $userSettings = $stmt->fetch();
    $userTheme = $userSettings ? $userSettings['theme'] : 'light';

} catch (PDOException $e) {
    error_log("Dashboard Error: " . $e->getMessage());
    $application = null;
    $notifications = [];
    $unreadCount = 0;
    $userTheme = 'light';
}

// Format application status for display
$applicationStatus = '';
$applicationDate = '';
$statusClass = '';

if ($application) {
    $applicationStatus = $application['status'];
    $applicationDate = date('m/d/Y', strtotime($application['submission_date']));

    switch ($applicationStatus) {
        case 'PENDING':
            $statusClass = 'pending';
            break;
        case 'APPROVED':
            $statusClass = 'approved';
            break;
        case 'REJECTED':
            $statusClass = 'rejected';
            break;
        case 'COMPLETED':
            $statusClass = 'completed';
            break;
        default:
            $statusClass = 'pending';
    }
}

// Get user initials for avatar
$initials = '';
if ($fullName) {
    $nameParts = explode(' ', $fullName);
    if (count($nameParts) >= 2) {
        $initials = substr($nameParts[0], 0, 1) . substr($nameParts[1], 0, 1);
    } else {
        $initials = substr($fullName, 0, 2);
    }
} else {
    $initials = substr($username, 0, 2);
}
$initials = strtoupper($initials);

// Format time for notifications
function timeAgo($timestamp) {
    $time = strtotime($timestamp);
    $now = time();
    $diff = $now - $time;

    if ($diff < 60) {
        return 'Just now';
    } elseif ($diff < 3600) {
        $minutes = floor($diff / 60);
        return $minutes . ' minute' . ($minutes > 1 ? 's' : '') . ' ago';
    } elseif ($diff < 86400) {
        $hours = floor($diff / 3600);
        return $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ago';
    } elseif ($diff < 604800) {
        $days = floor($diff / 86400);
        return $days . ' day' . ($days > 1 ? 's' : '') . ' ago';
    } else {
        return date('M j, Y', $time);
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Dashboard</title>
    <link rel="stylesheet" href="../css/dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="../js/theme-loader.js"></script>
    <?php include 'theme-sync.php'; ?>
</head>
<body>
    <div class="dashboard-container">
        <?php
        // Set current page for sidebar highlighting
        $currentPage = 'dashboard';

        // Include sidebar template
        include 'sidebar-template.php';
        ?>

        <!-- Main Content -->
        <div class="main-content">
            <div class="top-bar">
                <button type="button" class="menu-toggle" title="Toggle Menu">
                    <i class="fas fa-bars"></i>
                </button>

                <div class="top-icons">
                    <div class="icon-badge">
                        <i class="fas fa-bell"></i>
                        <?php if ($unreadCount > 0): ?>
                        <span class="badge"><?php echo $unreadCount; ?></span>
                        <?php endif; ?>
                    </div>
                    <div class="icon-badge">
                        <i class="fas fa-envelope"></i>
                        <span class="badge">0</span>
                    </div>
                </div>
            </div>

            <div class="dashboard-content">
                <div class="welcome-section">
                    <h1>Welcome back, <?php echo htmlspecialchars(explode(' ', $fullName)[0]); ?>!</h1>
                    <p>Here's what's happening with your student ID application</p>
                </div>

                <?php if ($application): ?>
                <div class="application-status">
                    <div class="status-icon">
                        <?php if ($applicationStatus === 'PENDING'): ?>
                        <i class="far fa-clock"></i>
                        <?php elseif ($applicationStatus === 'APPROVED'): ?>
                        <i class="fas fa-check-circle"></i>
                        <?php elseif ($applicationStatus === 'REJECTED'): ?>
                        <i class="fas fa-times-circle"></i>
                        <?php elseif ($applicationStatus === 'COMPLETED'): ?>
                        <i class="fas fa-id-card"></i>
                        <?php endif; ?>
                    </div>
                    <div class="status-details">
                        <h3>ID Card Application</h3>
                        <p class="status">Status: <span class="<?php echo $statusClass; ?>"><?php echo ucfirst(strtolower($applicationStatus)); ?></span></p>
                        <p class="date">Submitted: <?php echo $applicationDate; ?></p>
                    </div>
                </div>
                <?php else: ?>
                <div class="application-status">
                    <div class="status-icon">
                        <i class="fas fa-exclamation-circle"></i>
                    </div>
                    <div class="status-details">
                        <h3>No ID Card Application</h3>
                        <p>You haven't submitted an ID card application yet.</p>
                        <a href="createid.php" class="btn-primary">Apply Now</a>
                    </div>
                </div>
                <?php endif; ?>

                <div class="notifications-section">
                    <div class="section-header">
                        <h2>Recent Notifications</h2>
                        <a href="#" class="view-all">View All</a>
                    </div>

                    <?php if (count($notifications) > 0): ?>
                        <?php foreach ($notifications as $notification): ?>
                        <div class="notification-item">
                            <div class="notification-icon">
                                <?php if ($notification['notification_type'] === 'ID_CARD'): ?>
                                <i class="fas fa-id-card"></i>
                                <?php elseif ($notification['notification_type'] === 'DOCUMENT'): ?>
                                <i class="fas fa-file-alt"></i>
                                <?php elseif ($notification['notification_type'] === 'ACCOUNT'): ?>
                                <i class="fas fa-user-shield"></i>
                                <?php else: ?>
                                <i class="fas fa-bell"></i>
                                <?php endif; ?>
                            </div>
                            <div class="notification-content">
                                <h3><?php echo htmlspecialchars($notification['title']); ?></h3>
                                <p><?php echo htmlspecialchars($notification['message']); ?></p>
                                <p class="notification-time"><?php echo timeAgo($notification['created_at']); ?></p>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="notification-item">
                            <div class="notification-icon">
                                <i class="fas fa-info-circle"></i>
                            </div>
                            <div class="notification-content">
                                <h3>No Notifications</h3>
                                <p>You don't have any notifications yet.</p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="quick-actions">
                    <h2>Quick Actions</h2>
                    <div class="action-items">
                        <a href="createid.php" class="action-item">
                            <div class="action-icon">
                                <i class="fas fa-id-card"></i>
                            </div>
                            <p>Apply for ID Card</p>
                        </a>
                        <a href="documents.php" class="action-item">
                            <div class="action-icon">
                                <i class="fas fa-upload"></i>
                            </div>
                            <p>Upload Documents</p>
                        </a>
                        <a href="profile.php" class="action-item">
                            <div class="action-icon">
                                <i class="fas fa-user-edit"></i>
                            </div>
                            <p>Update Profile</p>
                        </a>
                    </div>
                </div>
            </div>

            <?php include 'footer-template.php'; ?>
        </div>
    </div>

    <script src="../js/dashboard.js"></script>
</body>
</html>
