<?php
/**
 * Face-API.js Model Download Script
 * Downloads required models from CDN and stores them locally
 */

set_time_limit(300); // 5 minutes timeout for downloads

// Base URL for Face-API.js models
$baseUrl = 'https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/models';

// Define models to download
$modelsToDownload = [
    // TinyFaceDetector - Lightweight and fast
    'tiny_face_detector' => [
        'tiny_face_detector_model-weights_manifest.json',
        'tiny_face_detector_model-shard1'
    ],
    
    // SSD MobileNetV1 - More accurate face detection
    'ssd_mobilenetv1' => [
        'ssd_mobilenetv1_model-weights_manifest.json',
        'ssd_mobilenetv1_model-shard1',
        'ssd_mobilenetv1_model-shard2'
    ],
    
    // Face Landmark 68 Point
    'face_landmark_68' => [
        'face_landmark_68_model-weights_manifest.json',
        'face_landmark_68_model-shard1'
    ],
    
    // Face Recognition
    'face_recognition' => [
        'face_recognition_model-weights_manifest.json',
        'face_recognition_model-shard1',
        'face_recognition_model-shard2'
    ],
    
    // Face Expression (optional)
    'face_expression' => [
        'face_expression_model-weights_manifest.json',
        'face_expression_model-shard1'
    ],
    
    // Age Gender (optional)
    'age_gender' => [
        'age_gender_model-weights_manifest.json',
        'age_gender_model-shard1'
    ]
];

/**
 * Download a file from URL and save it locally
 */
function downloadFile($url, $localPath) {
    $dir = dirname($localPath);
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Face-API Model Downloader');
    
    $data = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($httpCode !== 200 || $error) {
        throw new Exception("Failed to download $url: HTTP $httpCode, Error: $error");
    }
    
    if (file_put_contents($localPath, $data) === false) {
        throw new Exception("Failed to save file to $localPath");
    }
    
    return filesize($localPath);
}

// Start download process
echo "<!DOCTYPE html>\n<html>\n<head>\n<title>Face-API.js Model Downloader</title>\n";
echo "<style>body{font-family:Arial,sans-serif;margin:40px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>\n";
echo "</head>\n<body>\n";
echo "<h1>Face-API.js Model Downloader</h1>\n";

$totalDownloaded = 0;
$totalFiles = 0;
$errors = [];

foreach ($modelsToDownload as $modelType => $files) {
    echo "<h2>Downloading $modelType models...</h2>\n";
    
    foreach ($files as $file) {
        $totalFiles++;
        $url = "$baseUrl/$file";
        $localPath = __DIR__ . "/$modelType/$file";
        
        try {
            echo "<p class='info'>Downloading $file...</p>\n";
            flush();
            
            $size = downloadFile($url, $localPath);
            $totalDownloaded++;
            
            echo "<p class='success'>✓ Downloaded $file (" . number_format($size) . " bytes)</p>\n";
            
        } catch (Exception $e) {
            $errors[] = "Failed to download $file: " . $e->getMessage();
            echo "<p class='error'>✗ Failed to download $file: " . $e->getMessage() . "</p>\n";
        }
        
        flush();
    }
}

echo "<h2>Download Summary</h2>\n";
echo "<p><strong>Total files downloaded:</strong> $totalDownloaded / $totalFiles</p>\n";

if (empty($errors)) {
    echo "<p class='success'><strong>All models downloaded successfully!</strong></p>\n";
    echo "<p>You can now use the local model server for faster loading.</p>\n";
} else {
    echo "<p class='error'><strong>Some downloads failed:</strong></p>\n";
    echo "<ul>\n";
    foreach ($errors as $error) {
        echo "<li class='error'>$error</li>\n";
    }
    echo "</ul>\n";
}

echo "<p><a href='../html/createid.php'>← Back to Create ID Page</a></p>\n";
echo "</body>\n</html>";
?>
