<?php
/**
 * Create ID Page
 *
 * This is the page for creating a new student ID card.
 */

require_once '../backend/config.php';
require_once '../backend/auth.php';

// Redirect to login if not authenticated
if (!isLoggedIn()) {
    header('Location: ../index.php');
    exit;
}

// Get user data from session
$userId = $_SESSION['user_id'];
$username = $_SESSION['username'];
$fullName = $_SESSION['full_name'];
$studentId = $_SESSION['student_id'];

// Get user profile data
try {
    $db = getDbConnection();

    // Get user profile
    $stmt = $db->prepare("
        SELECT * FROM user_profiles
        WHERE user_id = ?
    ");
    $stmt->execute([$userId]);
    $profile = $stmt->fetch();

    // Check if user already has an ID application
    $stmt = $db->prepare("
        SELECT * FROM id_applications
        WHERE user_id = ?
        ORDER BY submission_date DESC
        LIMIT 1
    ");
    $stmt->execute([$userId]);
    $application = $stmt->fetch();

} catch (PDOException $e) {
    error_log("CreateID Error: " . $e->getMessage());
    $profile = null;
    $application = null;
}

// Get user initials for avatar
$initials = '';
if ($fullName) {
    $nameParts = explode(' ', $fullName);
    if (count($nameParts) >= 2) {
        $initials = substr($nameParts[0], 0, 1) . substr($nameParts[1], 0, 1);
    } else {
        $initials = substr($fullName, 0, 2);
    }
} else {
    $initials = substr($username, 0, 2);
}
$initials = strtoupper($initials);

// Generate CSRF token if it doesn't exist
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create New ID - Student Portal</title>
    <link rel="stylesheet" href="../css/dashboard.css">
    <link rel="stylesheet" href="../css/createid.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="../js/theme-loader.js"></script>
    <?php include 'theme-sync.php'; ?>
    <?php include 'message-count.php'; ?>
</head>
<body>
    <div class="dashboard-container">
        <?php
        // Set current page for sidebar highlighting
        $currentPage = 'createid';

        // Include sidebar template
        include 'sidebar-template.php';
        ?>

        <!-- Main Content -->
        <div class="main-content">
            <div class="top-bar">
                <button type="button" class="menu-toggle" title="Toggle Menu">
                    <i class="fas fa-bars"></i>
                </button>

                <div class="top-icons">
                    <div class="icon-badge">
                        <i class="fas fa-bell"></i>
                        <span class="badge">1</span>
                    </div>
                    <div class="icon-badge">
                        <i class="fas fa-envelope"></i>
                        <?php if ($messageCount > 0): ?>
                        <span class="badge"><?php echo $messageCount; ?></span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="dashboard-content">
                <div class="page-header">
                    <h1>Create New ID Card</h1>
                    <p>Fill out the form below to apply for a new student ID card</p>

                    <?php if (isset($_SESSION['createid_message'])): ?>
                        <div class="alert alert-success">
                            <?php
                                echo htmlspecialchars($_SESSION['createid_message']);
                                unset($_SESSION['createid_message']);
                            ?>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($_SESSION['createid_error'])): ?>
                        <div class="alert alert-error">
                            <?php
                                echo htmlspecialchars($_SESSION['createid_error']);
                                unset($_SESSION['createid_error']);
                            ?>
                        </div>
                    <?php endif; ?>
                </div>

                <?php if ($application && in_array($application['status'], ['PENDING', 'APPROVED'])): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <p>You already have an active ID card application with status: <strong><?php echo $application['status']; ?></strong>.</p>
                    <p>Submitted on: <?php echo date('F j, Y', strtotime($application['submission_date'])); ?></p>
                </div>
                <?php endif; ?>

                <div class="id-card-container">
                    <div class="id-card-form-section">
                        <div class="section-header">
                            <h2>User Type Selection</h2>
                            <p>Please select your user type to proceed</p>
                        </div>

                        <!-- User Type Selection -->
                        <div class="user-type-selection">
                            <div class="user-type-options">
                                <div class="user-type-option">
                                    <input type="radio" id="newUser" name="user_type" value="new" checked>
                                    <label for="newUser" class="user-type-label">
                                        <div class="user-type-icon">
                                            <i class="fas fa-user-plus"></i>
                                        </div>
                                        <div class="user-type-content">
                                            <h3>New User</h3>
                                            <p>First time applying for a student ID card</p>
                                        </div>
                                    </label>
                                </div>
                                <div class="user-type-option">
                                    <input type="radio" id="existingUser" name="user_type" value="existing">
                                    <label for="existingUser" class="user-type-label">
                                        <div class="user-type-icon">
                                            <i class="fas fa-user-check"></i>
                                        </div>
                                        <div class="user-type-content">
                                            <h3>Existing User</h3>
                                            <p>Renewal or replacement of existing ID card</p>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- New User Form -->
                        <div id="newUserForm" class="user-form-section">
                            <div class="section-header">
                                <h2>Personal Information</h2>
                                <p>Please provide accurate information for your ID card</p>
                            </div>

                            <form id="idCardForm" action="../backend/submit_id_application.php" method="post" enctype="multipart/form-data">
                                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                                <input type="hidden" name="application_type" value="NEW">
                                <input type="hidden" name="user_type" id="selectedUserType" value="new">

                            <div class="form-group">
                                <label for="fullName">Full Name (as it will appear on ID)</label>
                                <input type="text" id="fullName" name="full_name" placeholder="Enter your full name" value="<?php echo htmlspecialchars($fullName); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="studentId">Student ID Number</label>
                                <input type="text" id="studentId" name="student_id" placeholder="Enter your student ID number" value="<?php echo htmlspecialchars($studentId); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="department">Department/Faculty</label>
                                <select id="department" name="department" required>
                                    <option value="" disabled selected>Select your faculty</option>
                                    <option value="agriculture" <?php echo (isset($profile['department']) && $profile['department'] == 'Faculty of Agriculture') ? 'selected' : ''; ?>>Faculty of Agriculture</option>
                                    <option value="commerce" <?php echo (isset($profile['department']) && $profile['department'] == 'Faculty of Commerce') ? 'selected' : ''; ?>>Faculty of Commerce</option>
                                    <option value="consumer_science" <?php echo (isset($profile['department']) && $profile['department'] == 'Faculty of Consumer Science') ? 'selected' : ''; ?>>Faculty of Consumer Science</option>
                                    <option value="education" <?php echo (isset($profile['department']) && $profile['department'] == 'Faculty of Education') ? 'selected' : ''; ?>>Faculty of Education</option>
                                    <option value="health_science" <?php echo (isset($profile['department']) && $profile['department'] == 'Faculty of Health Science') ? 'selected' : ''; ?>>Faculty of Health Science</option>
                                    <option value="humanities" <?php echo (isset($profile['department']) && $profile['department'] == 'Faculty of Humanities') ? 'selected' : ''; ?>>Faculty of Humanities</option>
                                    <option value="science_engineering" <?php echo (isset($profile['department']) && $profile['department'] == 'Faculty of Science and Engineering') ? 'selected' : ''; ?>>Faculty of Science and Engineering</option>
                                    <option value="social_science" <?php echo (isset($profile['department']) && $profile['department'] == 'Faculty of Social Science') ? 'selected' : ''; ?>>Faculty of Social Science</option>
                                    <option value="certificates" <?php echo (isset($profile['department']) && $profile['department'] == 'Certificates') ? 'selected' : ''; ?>>Certificates</option>
                                    <option value="diplomas" <?php echo (isset($profile['department']) && $profile['department'] == 'Diplomas') ? 'selected' : ''; ?>>Diplomas</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="program">Program/Major</label>
                                <input type="text" id="program" name="program" placeholder="Enter your program or major" value="<?php echo isset($profile['program']) ? htmlspecialchars($profile['program']) : ''; ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="issued">Issued</label>
                                <input type="text" id="issued" name="issued" placeholder="dd/mm/yyyy" value="" readonly disabled>
                                <p class="field-info">This field will be populated when your application is approved by admin</p>
                            </div>

                            <!-- Step 1: Capture ID Document -->
                            <div class="form-group">
                                <label>Step 1: Capture ID Document</label>
                                <div class="capture-section" id="idCaptureSection">
                                    <div class="capture-instructions">
                                        <p class="id-instructions">Position your National Identity Card in the camera frame. Ensure good lighting and that the ID card is clearly visible.</p>
                                        <button type="button" id="captureIdBtn" class="capture-btn">
                                            <i class="fas fa-camera"></i> Capture ID Document
                                        </button>
                                    </div>

                                    <!-- Camera Interface for ID -->
                                    <div class="camera-interface" id="idCameraInterface" style="display: none;">
                                        <div class="camera-container">
                                            <video id="idCameraVideo" autoplay muted playsinline></video>
                                            <canvas id="idCameraCanvas" style="display: none;"></canvas>
                                            <div class="camera-overlay">
                                                <div class="camera-frame id-frame"></div>
                                            </div>
                                        </div>
                                        <div class="camera-controls">
                                            <button type="button" id="takeIdPhotoBtn" class="camera-btn capture">
                                                <i class="fas fa-camera"></i> Take Photo
                                            </button>
                                            <button type="button" id="cancelIdCameraBtn" class="camera-btn cancel">
                                                <i class="fas fa-times"></i> Cancel
                                            </button>
                                        </div>
                                    </div>

                                    <!-- ID Photo Preview -->
                                    <div class="photo-preview" id="idPhotoPreview" style="display: none;">
                                        <div class="preview-image" id="idPreviewImage"></div>
                                        <div class="preview-controls">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Step 2: Capture Face -->
                            <div class="form-group">
                                <label>Step 2: Capture Face</label>
                                <div class="capture-section" id="faceCaptureSection">
                                    <div class="capture-instructions">
                                        <p>Position your face in the camera frame for verification. Look directly at the camera.</p>
                                        <button type="button" id="captureFaceBtn" class="capture-btn">
                                            <i class="fas fa-user"></i> Capture Face
                                        </button>
                                    </div>

                                    <!-- Camera Interface for Face -->
                                    <div class="camera-interface" id="faceCameraInterface" style="display: none;">
                                        <div class="camera-container">
                                            <video id="faceCameraVideo" autoplay muted playsinline></video>
                                            <canvas id="faceCameraCanvas" style="display: none;"></canvas>
                                            <div class="camera-overlay">
                                                <div class="camera-frame face-frame"></div>
                                            </div>
                                        </div>
                                        <div class="camera-controls">
                                            <button type="button" id="takeFacePhotoBtn" class="camera-btn capture">
                                                <i class="fas fa-camera"></i> Take Photo
                                            </button>
                                            <button type="button" id="cancelFaceCameraBtn" class="camera-btn cancel">
                                                <i class="fas fa-times"></i> Cancel
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Face Photo Preview -->
                                    <div class="photo-preview" id="facePhotoPreview" style="display: none;">
                                        <div class="preview-image" id="facePreviewImage"></div>
                                        <div class="preview-controls">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Hidden inputs for captured data -->
                            <input type="hidden" id="idPhotoData" name="id_card_photo_data">
                            <input type="hidden" id="facePhotoData" name="face_photo_data">

                            <div class="form-group">
                                <label for="emergencyContact">Emergency Contact</label>
                                <input type="text" id="emergencyContact" name="emergency_contact" placeholder="Name and phone number" value="<?php echo isset($profile['emergency_contact']) ? htmlspecialchars($profile['emergency_contact']) : ''; ?>" required>
                            </div>

                            <!-- Required Documents Section -->
                            <div class="form-group">
                                <label>Required Documents</label>
                                <div class="document-upload-section">
                                    <div class="document-requirement">
                                        <h4><i class="fas fa-file-pdf"></i> Proof of Registration</h4>
                                        <p>Upload your current proof of registration document (PDF, JPG, or PNG format, max 5MB)</p>
                                    </div>

                                    <div class="file-upload-container" id="fileUploadContainer">
                                        <div class="file-drop-zone" id="fileDropZone">
                                            <div class="drop-zone-content">
                                                <i class="fas fa-cloud-upload-alt"></i>
                                                <p>Drag and drop your file here or <span class="browse-link">browse</span></p>
                                                <p class="file-requirements">Accepted formats: PDF, JPG, PNG (Max: 5MB)</p>
                                            </div>
                                            <input type="file" id="proofOfRegistration" name="proof_of_registration" accept=".pdf,.jpg,.jpeg,.png" style="display: none;" required>
                                        </div>

                                        <div class="file-preview" id="filePreview" style="display: none;">
                                            <div class="file-info">
                                                <i class="fas fa-file"></i>
                                                <div class="file-details">
                                                    <span class="file-name" id="fileName"></span>
                                                    <span class="file-size" id="fileSize"></span>
                                                </div>
                                                <button type="button" class="remove-file-btn" id="removeFileBtn">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group checkbox-group">
                                <input type="checkbox" id="termsAgree" name="terms_agree" required>
                                <label for="termsAgree">I confirm that the information provided is accurate and complete</label>
                            </div>

                                <button type="submit" class="submit-button">Submit Application</button>
                            </form>
                        </div>

                        <!-- Existing User Form -->
                        <div id="existingUserForm" class="user-form-section" style="display: none;">
                            <div class="section-header">
                                <h2>Update Information</h2>
                                <p>Update your information for ID card renewal or replacement</p>
                            </div>

                            <form id="existingUserIdCardForm" action="../backend/submit_id_application.php" method="post" enctype="multipart/form-data">
                                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                                <input type="hidden" name="application_type" value="RENEWAL">
                                <input type="hidden" name="user_type" id="selectedExistingUserType" value="existing">

                                <div class="form-group">
                                    <label for="existingFullName">Full Name</label>
                                    <input type="text" id="existingFullName" name="full_name" placeholder="Enter your full name" value="<?php echo htmlspecialchars($fullName); ?>" required>
                                </div>

                                <div class="form-group">
                                    <label for="existingStudentId">Student ID Number</label>
                                    <input type="text" id="existingStudentId" name="student_id" placeholder="Enter your student ID" value="<?php echo htmlspecialchars($studentId); ?>" readonly>
                                    <p class="field-info">Student ID cannot be changed</p>
                                </div>

                                <div class="form-group">
                                    <label for="existingDepartment">Faculty</label>
                                    <select id="existingDepartment" name="department" required>
                                        <option value="">Select your faculty</option>
                                        <option value="Agriculture" <?php echo (isset($profile['department']) && $profile['department'] === 'Agriculture') ? 'selected' : ''; ?>>Agriculture</option>
                                        <option value="Commerce" <?php echo (isset($profile['department']) && $profile['department'] === 'Commerce') ? 'selected' : ''; ?>>Commerce</option>
                                        <option value="Consumer Science" <?php echo (isset($profile['department']) && $profile['department'] === 'Consumer Science') ? 'selected' : ''; ?>>Consumer Science</option>
                                        <option value="Education" <?php echo (isset($profile['department']) && $profile['department'] === 'Education') ? 'selected' : ''; ?>>Education</option>
                                        <option value="Engineering" <?php echo (isset($profile['department']) && $profile['department'] === 'Engineering') ? 'selected' : ''; ?>>Engineering</option>
                                        <option value="Health Sciences" <?php echo (isset($profile['department']) && $profile['department'] === 'Health Sciences') ? 'selected' : ''; ?>>Health Sciences</option>
                                        <option value="Humanities" <?php echo (isset($profile['department']) && $profile['department'] === 'Humanities') ? 'selected' : ''; ?>>Humanities</option>
                                        <option value="Law" <?php echo (isset($profile['department']) && $profile['department'] === 'Law') ? 'selected' : ''; ?>>Law</option>
                                        <option value="Science" <?php echo (isset($profile['department']) && $profile['department'] === 'Science') ? 'selected' : ''; ?>>Science</option>
                                        <option value="Veterinary Science" <?php echo (isset($profile['department']) && $profile['department'] === 'Veterinary Science') ? 'selected' : ''; ?>>Veterinary Science</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="existingProgram">Program/Major</label>
                                    <input type="text" id="existingProgram" name="program" placeholder="e.g., Computer Science, Business Administration" value="<?php echo isset($profile['program']) ? htmlspecialchars($profile['program']) : ''; ?>" required>
                                </div>

                                <div class="form-group">
                                    <label for="existingIssued">Issued Date</label>
                                    <input type="text" id="existingIssued" name="issued" placeholder="dd/mm/yyyy" value="" readonly disabled>
                                    <p class="field-info">This field will be populated when your application is approved by admin</p>
                                </div>

                                <!-- Step 1: Capture ID Document -->
                                <div class="form-group">
                                    <label>Step 1: Capture ID Document</label>
                                    <div class="capture-section" id="existingIdCaptureSection">
                                        <div class="capture-instructions">
                                            <p class="id-instructions">Position your National Identity Card in the camera frame. Ensure good lighting and that the ID card is clearly visible.</p>
                                            <button type="button" id="existingCaptureIdBtn" class="capture-btn">
                                                <i class="fas fa-camera"></i> Capture ID Document
                                            </button>
                                        </div>

                                        <!-- Camera Interface for ID -->
                                        <div class="camera-interface" id="existingIdCameraInterface" style="display: none;">
                                            <div class="camera-container">
                                                <video id="existingIdCameraVideo" autoplay muted playsinline></video>
                                                <canvas id="existingIdCameraCanvas" style="display: none;"></canvas>
                                                <div class="camera-overlay">
                                                    <div class="camera-frame id-frame"></div>
                                                </div>
                                            </div>
                                            <div class="camera-controls">
                                                <button type="button" id="existingTakeIdPhotoBtn" class="camera-btn capture">
                                                    <i class="fas fa-camera"></i> Take Photo
                                                </button>
                                                <button type="button" id="existingCancelIdCameraBtn" class="camera-btn cancel">
                                                    <i class="fas fa-times"></i> Cancel
                                                </button>
                                            </div>
                                        </div>

                                        <!-- ID Photo Preview -->
                                        <div class="photo-preview" id="existingIdPhotoPreview" style="display: none;">
                                            <div class="preview-image" id="existingIdPreviewImage"></div>
                                            <div class="preview-controls">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Step 2: Capture Face -->
                                <div class="form-group">
                                    <label>Step 2: Capture Face</label>
                                    <div class="capture-section" id="existingFaceCaptureSection">
                                        <div class="capture-instructions">
                                            <p>Position your face in the camera frame for verification. Look directly at the camera.</p>
                                            <button type="button" id="existingCaptureFaceBtn" class="capture-btn">
                                                <i class="fas fa-user"></i> Capture Face
                                            </button>
                                        </div>

                                        <!-- Camera Interface for Face -->
                                        <div class="camera-interface" id="existingFaceCameraInterface" style="display: none;">
                                            <div class="camera-container">
                                                <video id="existingFaceCameraVideo" autoplay muted playsinline></video>
                                                <canvas id="existingFaceCameraCanvas" style="display: none;"></canvas>
                                                <div class="camera-overlay">
                                                    <div class="camera-frame face-frame"></div>
                                                </div>
                                            </div>
                                            <div class="camera-controls">
                                                <button type="button" id="existingTakeFacePhotoBtn" class="camera-btn capture">
                                                    <i class="fas fa-camera"></i> Take Photo
                                                </button>
                                                <button type="button" id="existingCancelFaceCameraBtn" class="camera-btn cancel">
                                                    <i class="fas fa-times"></i> Cancel
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Face Photo Preview -->
                                        <div class="photo-preview" id="existingFacePhotoPreview" style="display: none;">
                                            <div class="preview-image" id="existingFacePreviewImage"></div>
                                            <div class="preview-controls">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="existingEmergencyContact">Emergency Contact</label>
                                    <input type="text" id="existingEmergencyContact" name="emergency_contact" placeholder="Name and phone number" value="<?php echo isset($profile['emergency_contact']) ? htmlspecialchars($profile['emergency_contact']) : ''; ?>" required>
                                </div>

                                <!-- Required Documents Section -->
                                <div class="form-group">
                                    <label>Required Documents</label>
                                    <div class="document-upload-section">
                                        <div class="document-requirement">
                                            <h4><i class="fas fa-file-pdf"></i> Proof of Registration</h4>
                                            <p>Upload your current proof of registration document (PDF, JPG, or PNG format, max 5MB)</p>
                                        </div>

                                        <div class="file-upload-container" id="existingFileUploadContainer">
                                            <div class="file-drop-zone" id="existingFileDropZone">
                                                <div class="drop-zone-content">
                                                    <i class="fas fa-cloud-upload-alt"></i>
                                                    <p>Drag and drop your file here or <span class="browse-link">browse</span></p>
                                                    <p class="file-requirements">Accepted formats: PDF, JPG, PNG (Max: 5MB)</p>
                                                </div>
                                                <input type="file" id="existingProofOfRegistration" name="proof_of_registration" accept=".pdf,.jpg,.jpeg,.png" style="display: none;" required>
                                            </div>

                                            <div class="file-preview" id="existingFilePreview" style="display: none;">
                                                <div class="file-info">
                                                    <i class="fas fa-file"></i>
                                                    <div class="file-details">
                                                        <span class="file-name" id="existingFileName"></span>
                                                        <span class="file-size" id="existingFileSize"></span>
                                                    </div>
                                                    <button type="button" class="remove-file-btn" id="existingRemoveFileBtn">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Hidden inputs for captured data -->
                                <input type="hidden" id="existingIdPhotoData" name="id_card_photo_data">
                                <input type="hidden" id="existingFacePhotoData" name="face_photo_data">

                                <div class="form-group checkbox-group">
                                    <input type="checkbox" id="existingTermsAgree" name="terms_agree" required>
                                    <label for="existingTermsAgree">I confirm that the information provided is accurate and complete</label>
                                </div>

                                <button type="submit" class="submit-button">Submit Application</button>
                            </form>
                        </div>
                    </div>

                    <div class="id-card-preview-section">
                        <div class="section-header">
                            <h2>ID Card Preview</h2>
                            <p>This is how your ID card will look</p>
                        </div>

                        <div class="id-card-preview">
                            <div class="id-card">
                                <div class="id-card-header">
                                    <div class="university-logo">UNIVERSITY</div>
                                    <div class="card-type">STUDENT IDENTIFICATION</div>
                                </div>

                                <div class="id-card-photo" id="idCardPreviewPhoto">
                                    <i class="fas fa-user photo-icon"></i>
                                    <p class="photo-placeholder">Photo</p>
                                </div>

                                <div class="id-card-details">
                                    <div class="id-detail">
                                        <span class="detail-label">NAME</span>
                                        <span class="detail-value" id="previewName"><?php echo htmlspecialchars($fullName); ?></span>
                                    </div>

                                    <div class="id-detail">
                                        <span class="detail-label">ID</span>
                                        <span class="detail-value" id="previewId"><?php echo htmlspecialchars($studentId); ?></span>
                                    </div>

                                    <div class="id-detail">
                                        <span class="detail-label">DEPARTMENT</span>
                                        <span class="detail-value" id="previewDepartment"><?php echo isset($profile['department']) ? htmlspecialchars($profile['department']) : 'Engineering'; ?></span>
                                    </div>

                                    <div class="id-detail">
                                        <span class="detail-label">ISSUED</span>
                                        <span class="detail-value" id="previewIssued">Pending Approval</span>
                                    </div>
                                </div>

                                <div class="id-card-footer">
                                    <div class="barcode">|||||||||||||||||||||||</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <?php include 'footer-template.php'; ?>
        </div>
    </div>



    <script src="../js/dashboard.js"></script>
    <script src="../js/createid.js"></script>
    <script src="../js/simple-capture.js"></script>
    <script src="../js/document-upload.js"></script>
</body>
</html>
