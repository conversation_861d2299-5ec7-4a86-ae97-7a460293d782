<?php
/**
 * Create ID Page
 *
 * This is the page for creating a new student ID card.
 */

require_once '../backend/config.php';
require_once '../backend/auth.php';

// Redirect to login if not authenticated
if (!isLoggedIn()) {
    header('Location: ../index.php');
    exit;
}

// Get user data from session
$userId = $_SESSION['user_id'];
$username = $_SESSION['username'];
$fullName = $_SESSION['full_name'];
$studentId = $_SESSION['student_id'];

// Get user profile data
try {
    $db = getDbConnection();

    // Get user profile
    $stmt = $db->prepare("
        SELECT * FROM user_profiles
        WHERE user_id = ?
    ");
    $stmt->execute([$userId]);
    $profile = $stmt->fetch();

    // Check if user already has an ID application
    $stmt = $db->prepare("
        SELECT * FROM id_applications
        WHERE user_id = ?
        ORDER BY submission_date DESC
        LIMIT 1
    ");
    $stmt->execute([$userId]);
    $application = $stmt->fetch();

} catch (PDOException $e) {
    error_log("CreateID Error: " . $e->getMessage());
    $profile = null;
    $application = null;
}

// Get user initials for avatar
$initials = '';
if ($fullName) {
    $nameParts = explode(' ', $fullName);
    if (count($nameParts) >= 2) {
        $initials = substr($nameParts[0], 0, 1) . substr($nameParts[1], 0, 1);
    } else {
        $initials = substr($fullName, 0, 2);
    }
} else {
    $initials = substr($username, 0, 2);
}
$initials = strtoupper($initials);

// Generate CSRF token if it doesn't exist
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create New ID - Student Portal</title>
    <link rel="stylesheet" href="../css/dashboard.css">
    <link rel="stylesheet" href="../css/createid.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="../js/theme-loader.js"></script>
    <?php include 'theme-sync.php'; ?>
</head>
<body>
    <div class="dashboard-container">
        <?php
        // Set current page for sidebar highlighting
        $currentPage = 'createid';

        // Include sidebar template
        include 'sidebar-template.php';
        ?>

        <!-- Main Content -->
        <div class="main-content">
            <div class="top-bar">
                <button type="button" class="menu-toggle" title="Toggle Menu">
                    <i class="fas fa-bars"></i>
                </button>

                <div class="top-icons">
                    <div class="icon-badge">
                        <i class="fas fa-bell"></i>
                        <span class="badge">1</span>
                    </div>
                    <div class="icon-badge">
                        <i class="fas fa-envelope"></i>
                        <span class="badge">2</span>
                    </div>
                </div>
            </div>

            <div class="dashboard-content">
                <div class="page-header">
                    <h1>Create New ID Card</h1>
                    <p>Fill out the form below to apply for a new student ID card</p>
                </div>

                <?php if ($application && in_array($application['status'], ['PENDING', 'APPROVED'])): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <p>You already have an active ID card application with status: <strong><?php echo $application['status']; ?></strong>.</p>
                    <p>Submitted on: <?php echo date('F j, Y', strtotime($application['submission_date'])); ?></p>
                </div>
                <?php endif; ?>

                <div class="id-card-container">
                    <div class="id-card-form-section">
                        <div class="section-header">
                            <h2>Personal Information</h2>
                            <p>Please provide accurate information for your ID card</p>
                        </div>

                        <form id="idCardForm" action="../backend/submit_id_application.php" method="post" enctype="multipart/form-data">
                            <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                            <input type="hidden" name="application_type" value="NEW">

                            <div class="form-group">
                                <label for="fullName">Full Name (as it will appear on ID)</label>
                                <input type="text" id="fullName" name="full_name" placeholder="Enter your full name" value="<?php echo htmlspecialchars($fullName); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="studentId">Student ID Number</label>
                                <input type="text" id="studentId" name="student_id" placeholder="Enter your student ID number" value="<?php echo htmlspecialchars($studentId); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="department">Department/Faculty</label>
                                <select id="department" name="department" required>
                                    <option value="" disabled selected>Select your department</option>
                                    <option value="engineering" <?php echo (isset($profile['department']) && $profile['department'] == 'Engineering') ? 'selected' : ''; ?>>Engineering</option>
                                    <option value="science" <?php echo (isset($profile['department']) && $profile['department'] == 'Science') ? 'selected' : ''; ?>>Science</option>
                                    <option value="arts" <?php echo (isset($profile['department']) && $profile['department'] == 'Arts & Humanities') ? 'selected' : ''; ?>>Arts & Humanities</option>
                                    <option value="business" <?php echo (isset($profile['department']) && $profile['department'] == 'Business') ? 'selected' : ''; ?>>Business</option>
                                    <option value="medicine" <?php echo (isset($profile['department']) && $profile['department'] == 'Medicine') ? 'selected' : ''; ?>>Medicine</option>
                                    <option value="law" <?php echo (isset($profile['department']) && $profile['department'] == 'Law') ? 'selected' : ''; ?>>Law</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="program">Program/Major</label>
                                <input type="text" id="program" name="program" placeholder="Enter your program or major" value="<?php echo isset($profile['program']) ? htmlspecialchars($profile['program']) : ''; ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="enrollmentYear">Enrollment Year</label>
                                <input type="number" id="enrollmentYear" name="enrollment_year" min="2000" max="2030" placeholder="YYYY" value="<?php echo isset($profile['enrollment_year']) ? htmlspecialchars($profile['enrollment_year']) : ''; ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="expectedGraduation">Expected Graduation</label>
                                <input type="number" id="expectedGraduation" name="expected_graduation" min="2000" max="2030" placeholder="YYYY" value="<?php echo isset($profile['expected_graduation']) ? htmlspecialchars($profile['expected_graduation']) : ''; ?>" required>
                            </div>

                            <div class="form-group">
                                <label>ID Card Photo</label>
                                <div class="photo-upload-container">
                                    <div class="photo-preview">
                                        <?php if (isset($profile['profile_image']) && !empty($profile['profile_image'])): ?>
                                            <img src="<?php echo htmlspecialchars($profile['profile_image']); ?>" alt="Profile Photo">
                                        <?php else: ?>
                                            <i class="fas fa-user"></i>
                                        <?php endif; ?>
                                    </div>
                                    <div class="upload-button">
                                        <label for="photoUpload" class="upload-label">
                                            <i class="fas fa-upload"></i> Upload Photo
                                        </label>
                                        <input type="file" id="photoUpload" name="photo" accept="image/jpeg, image/png" hidden>
                                        <p class="file-info">JPG or PNG. Passport-style photo with clear background.</p>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="emergencyContact">Emergency Contact</label>
                                <input type="text" id="emergencyContact" name="emergency_contact" placeholder="Name and phone number" value="<?php echo isset($profile['emergency_contact']) ? htmlspecialchars($profile['emergency_contact']) : ''; ?>" required>
                            </div>

                            <div class="form-group checkbox-group">
                                <input type="checkbox" id="termsAgree" name="terms_agree" required>
                                <label for="termsAgree">I confirm that the information provided is accurate and complete</label>
                            </div>

                            <button type="submit" class="submit-button">Submit Application</button>
                        </form>
                    </div>

                    <div class="id-card-preview-section">
                        <div class="section-header">
                            <h2>ID Card Preview</h2>
                            <p>This is how your ID card will look</p>
                        </div>

                        <div class="id-card-preview">
                            <div class="id-card">
                                <div class="id-card-header">
                                    <div class="university-logo">UNIVERSITY</div>
                                    <div class="card-type">STUDENT IDENTIFICATION</div>
                                </div>

                                <div class="id-card-photo">
                                    <?php if (isset($profile['profile_image']) && !empty($profile['profile_image'])): ?>
                                        <img src="<?php echo htmlspecialchars($profile['profile_image']); ?>" alt="Profile Photo">
                                    <?php else: ?>
                                        <i class="fas fa-user"></i>
                                    <?php endif; ?>
                                </div>

                                <div class="id-card-details">
                                    <div class="id-detail">
                                        <span class="detail-label">NAME</span>
                                        <span class="detail-value" id="previewName"><?php echo htmlspecialchars($fullName); ?></span>
                                    </div>

                                    <div class="id-detail">
                                        <span class="detail-label">ID</span>
                                        <span class="detail-value" id="previewId"><?php echo htmlspecialchars($studentId); ?></span>
                                    </div>

                                    <div class="id-detail">
                                        <span class="detail-label">DEPARTMENT</span>
                                        <span class="detail-value" id="previewDepartment"><?php echo isset($profile['department']) ? htmlspecialchars($profile['department']) : 'Engineering'; ?></span>
                                    </div>

                                    <div class="id-detail">
                                        <span class="detail-label">VALID UNTIL</span>
                                        <span class="detail-value" id="previewExpiry"><?php echo isset($profile['expected_graduation']) ? htmlspecialchars($profile['expected_graduation']) : '2025'; ?></span>
                                    </div>
                                </div>

                                <div class="id-card-footer">
                                    <div class="barcode">|||||||||||||||||||||||</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <?php include 'footer-template.php'; ?>
        </div>
    </div>

    <script src="../js/dashboard.js"></script>
    <script src="../js/createid.js"></script>
</body>
</html>
