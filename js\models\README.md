# Face-API.js Models Directory

This directory should contain the Face-API.js model files required for facial verification.

## Required Model Files

Download these files from: https://github.com/justadudewhohacks/face-api.js-models

### 1. SSD MobileNet V1 (Face Detection)
- `ssd_mobilenetv1_model-weights_manifest.json`
- `ssd_mobilenetv1_model-shard1.bin`

### 2. Face Landmark 68 Model
- `face_landmark_68_model-weights_manifest.json`
- `face_landmark_68_model-shard1.bin`

### 3. Face Recognition Model
- `face_recognition_model-weights_manifest.json`
- `face_recognition_model-shard1.bin`
- `face_recognition_model-shard2.bin`

## Installation Instructions

1. Visit: https://github.com/justadudewhohacks/face-api.js-models
2. Download the required model files listed above
3. Place them directly in this `js/models/` directory
4. The facial verification system will automatically load these models when the page loads

## File Structure

After downloading, your directory should look like:

```
js/models/
├── README.md (this file)
├── ssd_mobilenetv1_model-weights_manifest.json
├── ssd_mobilenetv1_model-shard1.bin
├── face_landmark_68_model-weights_manifest.json
├── face_landmark_68_model-shard1.bin
├── face_recognition_model-weights_manifest.json
├── face_recognition_model-shard1.bin
└── face_recognition_model-shard2.bin
```

## Model Sizes

- SSD MobileNet V1: ~5.4 MB
- Face Landmark 68: ~350 KB
- Face Recognition: ~6.2 MB

Total: ~12 MB

## Usage

Once the models are in place, the facial verification system will:

1. Load models automatically when the page loads
2. Enable the "Verify Identity" button after both photos are captured
3. Compare facial features between ID document and face photos
4. Require successful verification before allowing form submission

## Troubleshooting

If facial verification is not working:

1. Check browser console for model loading errors
2. Ensure all model files are present and correctly named
3. Verify the Face-API.js library is properly loaded
4. Check network connectivity for model downloads
