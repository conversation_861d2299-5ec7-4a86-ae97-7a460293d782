<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facial Verification Test - Student ID Application</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary { background: #5e259b; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .test-results {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .hidden { display: none; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Facial Verification System Test</h1>
        <p>This page tests the facial verification implementation for the Student ID Application.</p>

        <div class="test-section">
            <h3>1. Library Loading Test</h3>
            <div id="libraryStatus" class="status info">Testing Face-API.js library loading...</div>
            <button onclick="testLibraryLoading()" class="btn-primary">Test Library</button>
        </div>

        <div class="test-section">
            <h3>2. Model Loading Test</h3>
            <div id="modelStatus" class="status info">Ready to test model loading...</div>
            <button onclick="testModelLoading()" class="btn-primary">Test Models</button>
        </div>

        <div class="test-section">
            <h3>3. Facial Verification System Test</h3>
            <div id="systemStatus" class="status info">Ready to test verification system...</div>
            <button onclick="testVerificationSystem()" class="btn-primary">Test System</button>
        </div>

        <div class="test-section">
            <h3>4. Integration Test</h3>
            <div id="integrationStatus" class="status info">Ready to test integration...</div>
            <button onclick="testIntegration()" class="btn-primary">Test Integration</button>
        </div>

        <div class="test-results">
            <h3>Test Results</h3>
            <div id="testResults">
                <p>Click the test buttons above to run diagnostics.</p>
            </div>
        </div>

        <div class="test-section">
            <h3>Implementation Status</h3>
            <div class="status warning">
                <strong>⚠️ Setup Required:</strong><br>
                1. Download Face-API.js library from: <a href="https://github.com/justadudewhohacks/face-api.js/blob/master/dist/face-api.min.js" target="_blank">GitHub</a><br>
                2. Replace js/face-api.min.js with the downloaded file<br>
                3. Download model files from: <a href="https://github.com/justadudewhohacks/face-api.js-models" target="_blank">Models Repository</a><br>
                4. Place model files in js/models/ directory
            </div>
        </div>
    </div>

    <!-- Include the same scripts as the main application -->
    <script src="js/face-api.min.js"></script>
    <script src="js/facial-verification.js"></script>

    <script>
        let testResults = [];

        function addTestResult(test, status, message) {
            testResults.push({ test, status, message, timestamp: new Date() });
            updateTestResults();
        }

        function updateTestResults() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = testResults.map(result => 
                `<div class="status ${result.status}">
                    <strong>${result.test}:</strong> ${result.message}
                    <small style="float: right;">${result.timestamp.toLocaleTimeString()}</small>
                </div>`
            ).join('');
        }

        function testLibraryLoading() {
            const statusDiv = document.getElementById('libraryStatus');
            statusDiv.className = 'status info';
            statusDiv.textContent = 'Testing Face-API.js library...';

            if (typeof faceapi !== 'undefined') {
                if (faceapi.nets && faceapi.detectSingleFace) {
                    statusDiv.className = 'status success';
                    statusDiv.textContent = '✅ Face-API.js library loaded successfully';
                    addTestResult('Library Loading', 'success', 'Face-API.js library is available');
                } else {
                    statusDiv.className = 'status warning';
                    statusDiv.textContent = '⚠️ Face-API.js placeholder detected - replace with actual library';
                    addTestResult('Library Loading', 'warning', 'Placeholder library detected - needs replacement');
                }
            } else {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ Face-API.js library not found';
                addTestResult('Library Loading', 'error', 'Face-API.js library not loaded');
            }
        }

        async function testModelLoading() {
            const statusDiv = document.getElementById('modelStatus');
            statusDiv.className = 'status info';
            statusDiv.textContent = 'Testing model loading...';

            try {
                const MODEL_URL = './js/models';
                
                // Test if models exist by attempting to load them
                await Promise.all([
                    faceapi.nets.ssdMobilenetv1.loadFromUri(MODEL_URL),
                    faceapi.nets.faceLandmark68Net.loadFromUri(MODEL_URL),
                    faceapi.nets.faceRecognitionNet.loadFromUri(MODEL_URL)
                ]);

                statusDiv.className = 'status success';
                statusDiv.textContent = '✅ All models loaded successfully';
                addTestResult('Model Loading', 'success', 'All required models loaded');
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = `❌ Model loading failed: ${error.message}`;
                addTestResult('Model Loading', 'error', `Model loading failed: ${error.message}`);
            }
        }

        function testVerificationSystem() {
            const statusDiv = document.getElementById('systemStatus');
            statusDiv.className = 'status info';
            statusDiv.textContent = 'Testing verification system...';

            if (typeof window.facialVerificationSystem !== 'undefined') {
                const system = window.facialVerificationSystem;
                
                if (system.modelsLoaded) {
                    statusDiv.className = 'status success';
                    statusDiv.textContent = '✅ Facial verification system initialized and models loaded';
                    addTestResult('Verification System', 'success', 'System initialized with models loaded');
                } else {
                    statusDiv.className = 'status warning';
                    statusDiv.textContent = '⚠️ Verification system initialized but models not loaded';
                    addTestResult('Verification System', 'warning', 'System initialized but models not loaded');
                }
            } else {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ Facial verification system not initialized';
                addTestResult('Verification System', 'error', 'Verification system not found');
            }
        }

        function testIntegration() {
            const statusDiv = document.getElementById('integrationStatus');
            statusDiv.className = 'status info';
            statusDiv.textContent = 'Testing integration...';

            const checks = [];

            // Check if CSS is loaded
            const verificationSection = document.querySelector('.verification-section');
            if (verificationSection || document.styleSheets.length > 0) {
                checks.push('✅ CSS styling available');
            } else {
                checks.push('❌ CSS styling not found');
            }

            // Check if capture system integration exists
            if (typeof window.simpleCaptureSystem !== 'undefined') {
                checks.push('✅ Capture system integration available');
            } else {
                checks.push('⚠️ Capture system not found (normal for test page)');
            }

            // Check if event system works
            try {
                document.dispatchEvent(new CustomEvent('photoCaptured', {
                    detail: { type: 'test', userType: 'new' }
                }));
                checks.push('✅ Event system functional');
            } catch (error) {
                checks.push('❌ Event system error: ' + error.message);
            }

            const allPassed = checks.every(check => check.startsWith('✅'));
            statusDiv.className = allPassed ? 'status success' : 'status warning';
            statusDiv.innerHTML = checks.join('<br>');
            
            addTestResult('Integration', allPassed ? 'success' : 'warning', 
                `${checks.filter(c => c.startsWith('✅')).length}/${checks.length} checks passed`);
        }

        // Auto-run basic tests on page load
        window.addEventListener('load', function() {
            setTimeout(() => {
                testLibraryLoading();
                setTimeout(() => testVerificationSystem(), 500);
            }, 1000);
        });
    </script>
</body>
</html>
