{"modelTopology": {"class_name": "Model", "config": {"name": "face_landmark_68", "layers": [{"name": "input_1", "class_name": "InputLayer", "config": {"batch_input_shape": [null, 112, 112, 1], "dtype": "float32", "sparse": false, "name": "input_1"}}]}}, "weightsManifest": [{"paths": ["face_landmark_68_model-shard1.bin"], "weights": [{"name": "conv2d_1/kernel", "shape": [3, 3, 1, 32], "dtype": "float32"}, {"name": "conv2d_1/bias", "shape": [32], "dtype": "float32"}, {"name": "dense_1/kernel", "shape": [2048, 136], "dtype": "float32"}, {"name": "dense_1/bias", "shape": [136], "dtype": "float32"}]}], "format": "layers-model", "generatedBy": "TensorFlow.js tfjs-layers v1.0.0", "convertedBy": "TensorFlow.js Converter v1.0.0"}