# Student ID Application

A comprehensive web application for managing student ID cards, including application, verification, and management.

## Project Structure

The project follows a structured approach with separate folders for different components:

```
Student ID Application/
├── css/                  # CSS stylesheets
├── js/                   # JavaScript files
├── html/                 # HTML pages (except index.html)
├── backend/              # PHP backend files
├── database/             # Database schema and scripts
├── uploads/              # File uploads (created at runtime)
└── index.html            # Main entry point
```

## Features

- **User Authentication**: Registration, login, and session management
- **Dashboard**: Overview of application status and notifications
- **ID Card Application**: Form for applying for a new student ID
- **Document Management**: Upload and manage required documents
- **Profile Management**: Update personal information
- **Settings**: Customize application preferences
- **Display & Accessibility**: Adjust visual and accessibility settings

## Pages

- **index.html**: Login and registration page
- **dashboard.html**: Main dashboard with application status
- **profile.html**: User profile management
- **createid.html**: ID card application form
- **documents.html**: Document upload and management
- **settings.html**: User settings and preferences
- **accessibility.html**: Display and accessibility options

## Backend

The backend is built with PHP and MySQL:

- **config.php**: Database and application configuration
- **auth.php**: Authentication and session management
- **api.php**: API endpoints for AJAX requests
- **login.php**: Login and registration handler
- **logout.php**: Logout handler

## Database

The MySQL database schema includes the following tables:

- **users**: User accounts and authentication
- **user_profiles**: User personal information
- **id_applications**: ID card applications
- **documents**: Uploaded documents
- **id_cards**: Issued ID cards
- **user_settings**: User preferences
- **notification_preferences**: Notification settings
- **notifications**: User notifications
- **admin_users**: Administrative users
- **activity_logs**: User activity tracking

## Setup Instructions

### Prerequisites

- Web server (Apache/Nginx)
- PHP 7.4 or higher
- MySQL 5.7 or higher

### Installation

1. Clone the repository to your web server directory
2. Create a MySQL database named `student_id_app`
3. Import the database schema from `database/schema.sql`
4. Configure database connection in `backend/config.php`
5. Ensure the `uploads` directory is writable by the web server
6. Access the application through your web browser

### Database Setup

```sql
CREATE DATABASE student_id_app;
USE student_id_app;
-- Import the schema.sql file
```

## Development

The application uses a simple architecture:

- HTML for structure
- CSS for styling
- JavaScript for client-side functionality
- PHP for server-side processing
- MySQL for data storage

## Future Enhancements

- Implement real-time notifications
- Add admin dashboard for application management
- Integrate with university systems
- Implement card printing functionality
- Add mobile app support

## Color Scheme

The application uses a purple-themed color scheme:

- Primary: #5e259b (Dark Purple)
- Secondary: #8a3df9 (Bright Purple)
- Background: #f0e6ff (Light Purple)
- Text: #333333 (Dark Gray)
- Accent: #ff5252 (Red)

## Contributors

- [Your Name] - Initial development

## License

This project is licensed under the MIT License - see the LICENSE file for details.
