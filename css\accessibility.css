/* Accessibility Page Styles */
.page-header {
    margin-bottom: 30px;
}

.page-header h1 {
    font-size: 24px;
    margin-bottom: 5px;
}

.page-header p {
    color: #666;
}

.accessibility-container {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.accessibility-section {
    background-color: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.section-header {
    margin-bottom: 25px;
}

.section-header h2 {
    font-size: 20px;
    margin-bottom: 5px;
}

.section-header p {
    color: #666;
    font-size: 14px;
}

/* Form Styling */
.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 12px;
    font-weight: 500;
}

.form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 15px center;
    background-size: 15px;
}

/* Theme Options Styling */
.theme-options {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.theme-option {
    flex: 1;
    min-width: 100px;
}

.theme-option input[type="radio"] {
    display: none;
}

.theme-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    padding: 10px;
    border-radius: 8px;
    border: 2px solid transparent;
    transition: all 0.3s;
}

.theme-preview {
    width: 100%;
    height: 80px;
    border-radius: 5px;
    margin-bottom: 10px;
}

.light-theme .theme-preview {
    background-color: #f5f7fa;
    border: 1px solid #ddd;
}

.dark-theme .theme-preview {
    background-color: #222;
    border: 1px solid #444;
}

.purple-theme .theme-preview {
    background-color: #5e259b;
    border: 1px solid #4a1d7a;
}

.blue-theme .theme-preview {
    background-color: #1976d2;
    border: 1px solid #1565c0;
}

input[type="radio"]:checked + .theme-label {
    border-color: #5e259b;
    background-color: #f0e6ff;
}

/* Range Slider Styling */
.range-slider {
    position: relative;
    width: 100%;
    padding: 10px 0;
}

.slider {
    -webkit-appearance: none;
    width: 100%;
    height: 6px;
    border-radius: 5px;
    background: #ddd;
    outline: none;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #5e259b;
    cursor: pointer;
}

.slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #5e259b;
    cursor: pointer;
    border: none;
}

.range-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
}

.range-labels span:first-child {
    font-size: 12px;
}

.range-labels span:nth-child(2) {
    font-size: 16px;
}

.range-labels span:last-child {
    font-size: 20px;
}

.range-value {
    position: absolute;
    right: 0;
    top: 0;
    font-size: 14px;
    color: #5e259b;
    font-weight: 500;
}

/* Toggle Switch Styling */
.toggle-group {
    margin-bottom: 20px;
}

.toggle-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #5e259b;
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.setting-description {
    font-size: 13px;
    color: #666;
    margin-top: 5px;
}

/* Save Button */
.save-button {
    background-color: #5e259b;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 12px 20px;
    font-size: 16px;
    cursor: pointer;
    margin-top: 10px;
}

.save-button:hover {
    background-color: #4a1d7a;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .theme-options {
        flex-direction: column;
    }
    
    .theme-option {
        width: 100%;
    }
    
    .theme-label {
        flex-direction: row;
    }
    
    .theme-preview {
        width: 60px;
        height: 60px;
        margin-bottom: 0;
        margin-right: 15px;
    }
}

@media (max-width: 576px) {
    .toggle-label {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .toggle-switch {
        margin-top: 10px;
    }
}
