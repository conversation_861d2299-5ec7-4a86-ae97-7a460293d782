/**
 * Document Upload System
 *
 * Handles file upload functionality for both New User and Existing User forms
 * Supports drag-and-drop, file validation, and preview
 */

class DocumentUploadSystem {
    constructor() {
        this.maxFileSize = 5 * 1024 * 1024; // 5MB in bytes
        this.allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
        this.allowedExtensions = ['.pdf', '.jpg', '.jpeg', '.png'];

        this.init();
    }

    init() {
        console.log('Initializing Document Upload System...');

        // Initialize for New User form
        this.initializeUpload('new');

        // Initialize for Existing User form
        this.initializeUpload('existing');

        console.log('Document Upload System initialized successfully');
    }

    initializeUpload(userType) {
        const prefix = userType === 'new' ? '' : 'existing';
        const dropZoneId = prefix ? `${prefix}FileDropZone` : 'fileDropZone';
        const fileInputId = prefix ? `${prefix}ProofOfRegistration` : 'proofOfRegistration';
        const filePreviewId = prefix ? `${prefix}FilePreview` : 'filePreview';
        const removeFileBtnId = prefix ? `${prefix}RemoveFileBtn` : 'removeFileBtn';

        const dropZone = document.getElementById(dropZoneId);
        const fileInput = document.getElementById(fileInputId);
        const filePreview = document.getElementById(filePreviewId);
        const removeFileBtn = document.getElementById(removeFileBtnId);

        if (!dropZone || !fileInput) {
            console.log(`Upload elements not found for ${userType} user form`);
            return;
        }

        console.log(`Setting up upload for ${userType} user form`);

        // Click to browse
        dropZone.addEventListener('click', () => {
            fileInput.click();
        });

        // File input change
        fileInput.addEventListener('change', (e) => {
            this.handleFileSelect(e.target.files[0], userType);
        });

        // Drag and drop events
        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('dragover');
        });

        dropZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            dropZone.classList.remove('dragover');
        });

        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleFileSelect(files[0], userType);
            }
        });

        // Remove file button
        if (removeFileBtn) {
            removeFileBtn.addEventListener('click', () => {
                this.removeFile(userType);
            });
        }
    }

    handleFileSelect(file, userType) {
        console.log(`File selected for ${userType} user:`, file.name);

        // Validate file
        const validation = this.validateFile(file);
        if (!validation.valid) {
            this.showError(validation.error, userType);
            return;
        }

        // Update file input
        const prefix = userType === 'new' ? '' : 'existing';
        const fileInputId = prefix ? `${prefix}ProofOfRegistration` : 'proofOfRegistration';
        const fileInput = document.getElementById(fileInputId);

        // Create a new FileList with the selected file
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(file);
        fileInput.files = dataTransfer.files;

        // Show file preview
        this.showFilePreview(file, userType);

        // Hide any previous errors
        this.hideError(userType);

        // Dispatch event for form validation update
        document.dispatchEvent(new CustomEvent('documentUploaded', {
            detail: { userType: userType, fileName: file.name }
        }));

        console.log(`File successfully selected: ${file.name} (${this.formatFileSize(file.size)})`);
    }

    validateFile(file) {
        // Check file size
        if (file.size > this.maxFileSize) {
            return {
                valid: false,
                error: `File size exceeds 5MB limit. Selected file is ${this.formatFileSize(file.size)}.`
            };
        }

        // Check file type
        if (!this.allowedTypes.includes(file.type)) {
            return {
                valid: false,
                error: 'Invalid file format. Please upload a PDF, JPG, or PNG file.'
            };
        }

        // Check file extension (additional validation)
        const extension = '.' + file.name.split('.').pop().toLowerCase();
        if (!this.allowedExtensions.includes(extension)) {
            return {
                valid: false,
                error: 'Invalid file extension. Please upload a PDF, JPG, or PNG file.'
            };
        }

        return { valid: true };
    }

    showFilePreview(file, userType) {
        const prefix = userType === 'new' ? '' : 'existing';
        const dropZoneId = prefix ? `${prefix}FileDropZone` : 'fileDropZone';
        const filePreviewId = prefix ? `${prefix}FilePreview` : 'filePreview';
        const fileNameId = prefix ? `${prefix}FileName` : 'fileName';
        const fileSizeId = prefix ? `${prefix}FileSize` : 'fileSize';

        const dropZone = document.getElementById(dropZoneId);
        const filePreview = document.getElementById(filePreviewId);
        const fileName = document.getElementById(fileNameId);
        const fileSize = document.getElementById(fileSizeId);

        if (dropZone && filePreview && fileName && fileSize) {
            // Hide drop zone and show preview
            dropZone.style.display = 'none';
            filePreview.style.display = 'block';

            // Update file details
            fileName.textContent = file.name;
            fileSize.textContent = this.formatFileSize(file.size);
        }
    }

    removeFile(userType) {
        console.log(`Removing file for ${userType} user`);

        const prefix = userType === 'new' ? '' : 'existing';
        const dropZoneId = prefix ? `${prefix}FileDropZone` : 'fileDropZone';
        const filePreviewId = prefix ? `${prefix}FilePreview` : 'filePreview';
        const fileInputId = prefix ? `${prefix}ProofOfRegistration` : 'proofOfRegistration';

        const dropZone = document.getElementById(dropZoneId);
        const filePreview = document.getElementById(filePreviewId);
        const fileInput = document.getElementById(fileInputId);

        if (dropZone && filePreview && fileInput) {
            // Clear file input
            fileInput.value = '';

            // Show drop zone and hide preview
            dropZone.style.display = 'block';
            filePreview.style.display = 'none';

            // Hide any errors
            this.hideError(userType);
        }
    }

    showError(message, userType) {
        console.error(`File upload error for ${userType} user:`, message);

        const prefix = userType === 'new' ? '' : 'existing';
        const containerId = prefix ? `${prefix}FileUploadContainer` : 'fileUploadContainer';
        const container = document.getElementById(containerId);

        if (container) {
            // Remove existing error
            this.hideError(userType);

            // Create error element
            const errorDiv = document.createElement('div');
            errorDiv.className = 'file-error';
            errorDiv.id = prefix ? `${prefix}FileError` : 'fileError';
            errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;

            container.appendChild(errorDiv);
        }
    }

    hideError(userType) {
        const prefix = userType === 'new' ? '' : 'existing';
        const errorId = prefix ? `${prefix}FileError` : 'fileError';
        const existingError = document.getElementById(errorId);

        if (existingError) {
            existingError.remove();
        }
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Validation method for form submission
    validateFormSubmission(userType) {
        const prefix = userType === 'new' ? '' : 'existing';
        const fileInputId = prefix ? `${prefix}ProofOfRegistration` : 'proofOfRegistration';
        const fileInput = document.getElementById(fileInputId);

        if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
            this.showError('Please upload a proof of registration document before submitting.', userType);
            return false;
        }

        const file = fileInput.files[0];
        const validation = this.validateFile(file);

        if (!validation.valid) {
            this.showError(validation.error, userType);
            return false;
        }

        return true;
    }
}

// Initialize the document upload system when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing Document Upload System...');
    window.documentUploadSystem = new DocumentUploadSystem();
});
