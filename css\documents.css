/* Documents Page Styles */
.page-header {
    margin-bottom: 30px;
}

.page-header h1 {
    font-size: 24px;
    margin-bottom: 5px;
}

.page-header p {
    color: #666;
}

.documents-container {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.documents-section {
    background-color: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.section-header {
    margin-bottom: 20px;
}

.section-header h2 {
    font-size: 18px;
    margin-bottom: 5px;
}

.section-header p {
    color: #666;
    font-size: 14px;
}

/* Document List Styling */
.document-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.document-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border: 1px solid #eee;
    border-radius: 8px;
    transition: all 0.3s;
}

.document-item:hover {
    border-color: #ddd;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.document-icon {
    width: 50px;
    height: 50px;
    background-color: #f0e6ff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: #5e259b;
    font-size: 20px;
}

.document-details {
    flex: 1;
}

.document-details h3 {
    font-size: 16px;
    margin-bottom: 5px;
}

.document-details p {
    color: #666;
    font-size: 14px;
    margin-bottom: 8px;
}

.document-status {
    font-size: 13px;
    display: flex;
    align-items: center;
}

.document-status i {
    margin-right: 5px;
}

.document-status.incomplete {
    color: #ff5252;
}

.document-status.complete {
    color: #4caf50;
}

.document-status.optional {
    color: #2196f3;
}

.document-actions {
    display: flex;
    gap: 10px;
}

.upload-btn, .view-btn, .delete-btn {
    border: none;
    border-radius: 5px;
    padding: 8px 12px;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
}

.upload-btn {
    background-color: #5e259b;
    color: white;
}

.upload-btn:hover {
    background-color: #4a1d7a;
}

.view-btn {
    background-color: #f0e6ff;
    color: #5e259b;
}

.view-btn:hover {
    background-color: #e5d5ff;
}

.delete-btn {
    background-color: #fff0f0;
    color: #ff5252;
    width: 36px;
    height: 36px;
    justify-content: center;
}

.delete-btn:hover {
    background-color: #ffe0e0;
}

.upload-btn i, .view-btn i {
    margin-right: 5px;
}

/* Upload New Document Button */
.upload-new-document {
    display: flex;
    justify-content: center;
    margin-top: 10px;
}

.new-document-btn {
    background-color: #5e259b;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 12px 20px;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
}

.new-document-btn:hover {
    background-color: #4a1d7a;
}

.new-document-btn i {
    margin-right: 8px;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .document-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .document-icon {
        margin-bottom: 15px;
    }
    
    .document-details {
        margin-bottom: 15px;
        width: 100%;
    }
    
    .document-actions {
        width: 100%;
        justify-content: flex-end;
    }
}

@media (max-width: 576px) {
    .document-actions {
        flex-wrap: wrap;
    }
    
    .upload-btn, .view-btn, .delete-btn {
        width: 100%;
        justify-content: center;
        margin-top: 5px;
    }
}
