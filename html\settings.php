<?php
/**
 * Settings Page
 *
 * This is the settings page for authenticated users.
 */

require_once '../backend/config.php';
require_once '../backend/auth.php';

// Redirect to login if not authenticated
if (!isLoggedIn()) {
    header('Location: ../index.php');
    exit;
}

// Get user data from session
$userId = $_SESSION['user_id'];
$username = $_SESSION['username'];
$fullName = $_SESSION['full_name'];
$studentId = $_SESSION['student_id'];

// Get user settings
try {
    $db = getDbConnection();

    // Get user profile and settings
    $stmt = $db->prepare("
        SELECT u.email, p.* FROM users u
        LEFT JOIN user_profiles p ON u.user_id = p.user_id
        WHERE u.user_id = ?
    ");
    $stmt->execute([$userId]);
    $userProfile = $stmt->fetch();

    // Get user notification settings
    $stmt = $db->prepare("
        SELECT * FROM user_settings
        WHERE user_id = ?
    ");
    $stmt->execute([$userId]);
    $userSettings = $stmt->fetch();

} catch (PDOException $e) {
    error_log("Settings Error: " . $e->getMessage());
    $userProfile = null;
    $userSettings = null;
}

// Get user initials for avatar
$initials = '';
if ($fullName) {
    $nameParts = explode(' ', $fullName);
    if (count($nameParts) >= 2) {
        $initials = substr($nameParts[0], 0, 1) . substr($nameParts[1], 0, 1);
    } else {
        $initials = substr($fullName, 0, 2);
    }
} else {
    $initials = substr($username, 0, 2);
}
$initials = strtoupper($initials);

// Generate CSRF token if it doesn't exist
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - Student Portal</title>
    <link rel="stylesheet" href="../css/dashboard.css">
    <link rel="stylesheet" href="../css/settings.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="../js/theme-loader.js"></script>
</head>
<body>
    <div class="dashboard-container">
        <?php
        // Set current page for sidebar highlighting
        $currentPage = 'settings';

        // Include sidebar template
        include 'sidebar-template.php';
        ?>

        <!-- Main Content -->
        <div class="main-content">
            <div class="top-bar">
                <button type="button" class="menu-toggle" title="Toggle Menu">
                    <i class="fas fa-bars"></i>
                </button>

                <div class="top-icons">
                    <div class="icon-badge">
                        <i class="fas fa-bell"></i>
                        <span class="badge">1</span>
                    </div>
                    <div class="icon-badge">
                        <i class="fas fa-envelope"></i>
                        <span class="badge">2</span>
                    </div>
                </div>
            </div>

            <div class="dashboard-content">
                <div class="page-header">
                    <h1>Settings</h1>
                    <p>Manage your account settings and preferences</p>
                </div>

                <div class="settings-container">
                    <div class="settings-sidebar">
                        <div class="settings-nav">
                            <a href="#account" class="settings-nav-item active" data-target="account-settings">
                                <i class="fas fa-user-cog"></i> Account
                            </a>
                            <a href="#security" class="settings-nav-item" data-target="security-settings">
                                <i class="fas fa-shield-alt"></i> Security
                            </a>
                            <a href="#notifications" class="settings-nav-item" data-target="notification-settings">
                                <i class="fas fa-bell"></i> Notifications
                            </a>
                            <a href="#privacy" class="settings-nav-item" data-target="privacy-settings">
                                <i class="fas fa-user-shield"></i> Privacy
                            </a>
                            <a href="#connected" class="settings-nav-item" data-target="connected-accounts">
                                <i class="fas fa-link"></i> Connected Accounts
                            </a>
                        </div>
                    </div>

                    <div class="settings-content">
                        <!-- Account Settings -->
                        <div id="account-settings" class="settings-section active">
                            <div class="section-header">
                                <h2>Account Settings</h2>
                                <p>Manage your account information</p>
                            </div>

                            <form id="accountSettingsForm" action="../backend/update_settings.php" method="post">
                                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                                <input type="hidden" name="settings_type" value="account">

                                <div class="form-group">
                                    <label for="email">Email Address</label>
                                    <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($userProfile['email'] ?? ''); ?>">
                                </div>

                                <div class="form-group">
                                    <label for="phone">Phone Number</label>
                                    <input type="tel" id="phone" name="phone" value="<?php echo htmlspecialchars($userProfile['phone'] ?? ''); ?>">
                                </div>

                                <div class="form-group">
                                    <label for="language">Preferred Language</label>
                                    <select id="language" name="language">
                                        <option value="en" <?php echo (isset($userSettings['language']) && $userSettings['language'] == 'en') ? 'selected' : ''; ?>>English</option>
                                        <option value="es" <?php echo (isset($userSettings['language']) && $userSettings['language'] == 'es') ? 'selected' : ''; ?>>Spanish</option>
                                        <option value="fr" <?php echo (isset($userSettings['language']) && $userSettings['language'] == 'fr') ? 'selected' : ''; ?>>French</option>
                                        <option value="de" <?php echo (isset($userSettings['language']) && $userSettings['language'] == 'de') ? 'selected' : ''; ?>>German</option>
                                        <option value="zh" <?php echo (isset($userSettings['language']) && $userSettings['language'] == 'zh') ? 'selected' : ''; ?>>Chinese</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="timezone">Time Zone</label>
                                    <select id="timezone" name="timezone">
                                        <option value="utc-8" <?php echo (isset($userSettings['timezone']) && $userSettings['timezone'] == 'utc-8') ? 'selected' : ''; ?>>Pacific Time (UTC-8)</option>
                                        <option value="utc-7" <?php echo (isset($userSettings['timezone']) && $userSettings['timezone'] == 'utc-7') ? 'selected' : ''; ?>>Mountain Time (UTC-7)</option>
                                        <option value="utc-6" <?php echo (isset($userSettings['timezone']) && $userSettings['timezone'] == 'utc-6') ? 'selected' : ''; ?>>Central Time (UTC-6)</option>
                                        <option value="utc-5" <?php echo (isset($userSettings['timezone']) && $userSettings['timezone'] == 'utc-5') ? 'selected' : ''; ?>>Eastern Time (UTC-5)</option>
                                        <option value="utc" <?php echo (isset($userSettings['timezone']) && $userSettings['timezone'] == 'utc') ? 'selected' : ''; ?>>UTC</option>
                                        <option value="utc+1" <?php echo (isset($userSettings['timezone']) && $userSettings['timezone'] == 'utc+1') ? 'selected' : ''; ?>>Central European Time (UTC+1)</option>
                                    </select>
                                </div>

                                <button type="submit" class="save-button">Save Changes</button>
                            </form>
                        </div>

                        <!-- Security Settings -->
                        <div id="security-settings" class="settings-section">
                            <div class="section-header">
                                <h2>Security Settings</h2>
                                <p>Manage your account security</p>
                            </div>

                            <form id="securitySettingsForm" action="../backend/update_settings.php" method="post">
                                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                                <input type="hidden" name="settings_type" value="security">

                                <div class="form-group">
                                    <label for="current-password">Current Password</label>
                                    <input type="password" id="current-password" name="current_password" placeholder="Enter your current password">
                                </div>

                                <div class="form-group">
                                    <label for="new-password">New Password</label>
                                    <input type="password" id="new-password" name="new_password" placeholder="Enter new password">
                                </div>

                                <div class="form-group">
                                    <label for="confirm-new-password">Confirm New Password</label>
                                    <input type="password" id="confirm-new-password" name="confirm_password" placeholder="Confirm new password">
                                </div>

                                <div class="form-group toggle-group">
                                    <label class="toggle-label">
                                        <span>Two-Factor Authentication</span>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="twoFactorAuth" name="two_factor_auth" <?php echo (isset($userSettings['two_factor_auth']) && $userSettings['two_factor_auth'] == 1) ? 'checked' : ''; ?>>
                                            <span class="toggle-slider"></span>
                                        </div>
                                    </label>
                                    <p class="setting-description">Add an extra layer of security to your account</p>
                                </div>

                                <div class="form-group toggle-group">
                                    <label class="toggle-label">
                                        <span>Login Notifications</span>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="loginNotifications" name="login_notifications" <?php echo (isset($userSettings['login_notifications']) && $userSettings['login_notifications'] == 1) ? 'checked' : ''; ?>>
                                            <span class="toggle-slider"></span>
                                        </div>
                                    </label>
                                    <p class="setting-description">Receive notifications when your account is accessed from a new device</p>
                                </div>

                                <button type="submit" class="save-button">Update Security Settings</button>
                            </form>
                        </div>

                        <!-- Notification Settings -->
                        <div id="notification-settings" class="settings-section">
                            <div class="section-header">
                                <h2>Notification Settings</h2>
                                <p>Manage how you receive notifications</p>
                            </div>

                            <form id="notificationSettingsForm" action="../backend/update_settings.php" method="post">
                                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                                <input type="hidden" name="settings_type" value="notifications">

                                <div class="form-group toggle-group">
                                    <label class="toggle-label">
                                        <span>Email Notifications</span>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="emailNotifications" name="email_notifications" <?php echo (isset($userSettings['email_notifications']) && $userSettings['email_notifications'] == 1) ? 'checked' : ''; ?>>
                                            <span class="toggle-slider"></span>
                                        </div>
                                    </label>
                                </div>

                                <div class="form-group toggle-group">
                                    <label class="toggle-label">
                                        <span>SMS Notifications</span>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="smsNotifications" name="sms_notifications" <?php echo (isset($userSettings['sms_notifications']) && $userSettings['sms_notifications'] == 1) ? 'checked' : ''; ?>>
                                            <span class="toggle-slider"></span>
                                        </div>
                                    </label>
                                </div>

                                <div class="form-group toggle-group">
                                    <label class="toggle-label">
                                        <span>Browser Notifications</span>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="browserNotifications" name="browser_notifications" <?php echo (isset($userSettings['browser_notifications']) && $userSettings['browser_notifications'] == 1) ? 'checked' : ''; ?>>
                                            <span class="toggle-slider"></span>
                                        </div>
                                    </label>
                                </div>

                                <h3 class="settings-subheader">Notification Types</h3>

                                <div class="form-group checkbox-group">
                                    <input type="checkbox" id="idCardUpdates" name="id_card_updates" <?php echo (isset($userSettings['id_card_updates']) && $userSettings['id_card_updates'] == 1) ? 'checked' : ''; ?>>
                                    <label for="idCardUpdates">ID Card Application Updates</label>
                                </div>

                                <div class="form-group checkbox-group">
                                    <input type="checkbox" id="documentReminders" name="document_reminders" <?php echo (isset($userSettings['document_reminders']) && $userSettings['document_reminders'] == 1) ? 'checked' : ''; ?>>
                                    <label for="documentReminders">Document Submission Reminders</label>
                                </div>

                                <div class="form-group checkbox-group">
                                    <input type="checkbox" id="accountActivity" name="account_activity" <?php echo (isset($userSettings['account_activity']) && $userSettings['account_activity'] == 1) ? 'checked' : ''; ?>>
                                    <label for="accountActivity">Account Activity</label>
                                </div>

                                <div class="form-group checkbox-group">
                                    <input type="checkbox" id="newsUpdates" name="news_updates" <?php echo (isset($userSettings['news_updates']) && $userSettings['news_updates'] == 1) ? 'checked' : ''; ?>>
                                    <label for="newsUpdates">News and Updates</label>
                                </div>

                                <button type="submit" class="save-button">Save Notification Preferences</button>
                            </form>
                        </div>

                        <!-- Privacy Settings -->
                        <div id="privacy-settings" class="settings-section">
                            <!-- Privacy settings content here -->
                        </div>

                        <!-- Connected Accounts -->
                        <div id="connected-accounts" class="settings-section">
                            <!-- Connected accounts content here -->
                        </div>
                    </div>
                </div>
            </div>

            <?php include 'footer-template.php'; ?>
        </div>
    </div>

    <script src="../js/dashboard.js"></script>
    <script src="../js/settings.js"></script>
</body>
</html>
