-- Migration script to update faculty/department options and add issued date functionality
-- This supports the new two-tier user selection system

USE student_id_app;

-- Update existing department values to match new faculty structure
UPDATE user_profiles 
SET department = CASE 
    WHEN department = 'Engineering' THEN 'Faculty of Science and Engineering'
    WHEN department = 'Science' THEN 'Faculty of Science and Engineering'
    WHEN department = 'Arts & Humanities' THEN 'Faculty of Humanities'
    WHEN department = 'Business' THEN 'Faculty of Commerce'
    WHEN department = 'Medicine' THEN 'Faculty of Health Science'
    WHEN department = 'Law' THEN 'Faculty of Social Science'
    ELSE department
END
WHERE department IN ('Engineering', 'Science', 'Arts & Humanities', 'Business', 'Medicine', 'Law');

-- Add issued_date column to id_applications table for admin approval tracking
ALTER TABLE id_applications 
ADD COLUMN issued_date DATE NULL 
AFTER id_card_photo;

-- Add user_type column to id_applications table to track application type
ALTER TABLE id_applications 
ADD COLUMN user_type ENUM('new', 'existing') DEFAULT 'new' 
AFTER application_type;

-- Add index for better performance on new columns
CREATE INDEX idx_id_applications_issued_date ON id_applications(issued_date);
CREATE INDEX idx_id_applications_user_type ON id_applications(user_type);

-- Update existing applications to have 'new' user type
UPDATE id_applications 
SET user_type = 'new' 
WHERE user_type IS NULL;

-- Add comments to document the new columns
ALTER TABLE id_applications 
MODIFY COLUMN issued_date DATE NULL 
COMMENT 'Date when the ID card was issued/approved by admin (dd/mm/yyyy format)';

ALTER TABLE id_applications 
MODIFY COLUMN user_type ENUM('new', 'existing') DEFAULT 'new' 
COMMENT 'Type of user application: new (first time) or existing (renewal/replacement)';

-- Sample data update for testing
UPDATE user_profiles 
SET department = 'Faculty of Science and Engineering' 
WHERE user_id = 1;

UPDATE user_profiles 
SET department = 'Faculty of Health Science' 
WHERE user_id = 2;
