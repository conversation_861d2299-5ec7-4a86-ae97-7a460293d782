document.addEventListener('DOMContentLoaded', function() {
    // Upload buttons functionality
    const uploadButtons = document.querySelectorAll('.upload-btn');
    
    uploadButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Create a file input element
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.accept = '.pdf,.doc,.docx,.jpg,.jpeg,.png';
            fileInput.style.display = 'none';
            
            // Append to body and trigger click
            document.body.appendChild(fileInput);
            fileInput.click();
            
            // Handle file selection
            fileInput.addEventListener('change', function() {
                const file = this.files[0];
                if (file) {
                    // Get the document item that contains the clicked button
                    const documentItem = button.closest('.document-item');
                    const documentDetails = documentItem.querySelector('.document-details');
                    const documentStatus = documentItem.querySelector('.document-status');
                    const documentActions = documentItem.querySelector('.document-actions');
                    
                    // Update status to complete
                    documentStatus.className = 'document-status complete';
                    documentStatus.innerHTML = '<i class="fas fa-check-circle"></i> Uploaded';
                    
                    // Update actions
                    documentActions.innerHTML = `
                        <button class="view-btn">
                            <i class="fas fa-eye"></i> View
                        </button>
                        <button class="delete-btn">
                            <i class="fas fa-trash"></i>
                        </button>
                    `;
                    
                    // Add event listeners to new buttons
                    const viewBtn = documentActions.querySelector('.view-btn');
                    const deleteBtn = documentActions.querySelector('.delete-btn');
                    
                    viewBtn.addEventListener('click', handleViewDocument);
                    deleteBtn.addEventListener('click', handleDeleteDocument);
                    
                    // Show success message
                    alert(`File "${file.name}" uploaded successfully!`);
                }
                
                // Remove the temporary input element
                document.body.removeChild(fileInput);
            });
        });
    });
    
    // View document functionality
    const viewButtons = document.querySelectorAll('.view-btn');
    
    viewButtons.forEach(button => {
        button.addEventListener('click', handleViewDocument);
    });
    
    function handleViewDocument(e) {
        // In a real application, this would open the document
        // For now, just show an alert
        const documentItem = this.closest('.document-item');
        const documentTitle = documentItem.querySelector('h3').textContent;
        
        alert(`Viewing document: ${documentTitle}`);
    }
    
    // Delete document functionality
    const deleteButtons = document.querySelectorAll('.delete-btn');
    
    deleteButtons.forEach(button => {
        button.addEventListener('click', handleDeleteDocument);
    });
    
    function handleDeleteDocument(e) {
        const documentItem = this.closest('.document-item');
        const documentTitle = documentItem.querySelector('h3').textContent;
        
        // Confirm deletion
        if (confirm(`Are you sure you want to delete "${documentTitle}"?`)) {
            // Get the document details and actions
            const documentDetails = documentItem.querySelector('.document-details');
            const documentStatus = documentItem.querySelector('.document-status');
            const documentActions = documentItem.querySelector('.document-actions');
            
            // Update status to incomplete
            documentStatus.className = 'document-status incomplete';
            documentStatus.innerHTML = '<i class="fas fa-exclamation-circle"></i> Required';
            
            // Update actions
            documentActions.innerHTML = `
                <button class="upload-btn">
                    <i class="fas fa-upload"></i> Upload
                </button>
            `;
            
            // Add event listener to new upload button
            const uploadBtn = documentActions.querySelector('.upload-btn');
            uploadBtn.addEventListener('click', function() {
                const fileInput = document.createElement('input');
                fileInput.type = 'file';
                fileInput.accept = '.pdf,.doc,.docx,.jpg,.jpeg,.png';
                fileInput.style.display = 'none';
                
                document.body.appendChild(fileInput);
                fileInput.click();
                
                fileInput.addEventListener('change', function() {
                    const file = this.files[0];
                    if (file) {
                        // Update status to complete
                        documentStatus.className = 'document-status complete';
                        documentStatus.innerHTML = '<i class="fas fa-check-circle"></i> Uploaded';
                        
                        // Update actions
                        documentActions.innerHTML = `
                            <button class="view-btn">
                                <i class="fas fa-eye"></i> View
                            </button>
                            <button class="delete-btn">
                                <i class="fas fa-trash"></i>
                            </button>
                        `;
                        
                        // Add event listeners to new buttons
                        const viewBtn = documentActions.querySelector('.view-btn');
                        const deleteBtn = documentActions.querySelector('.delete-btn');
                        
                        viewBtn.addEventListener('click', handleViewDocument);
                        deleteBtn.addEventListener('click', handleDeleteDocument);
                        
                        // Show success message
                        alert(`File "${file.name}" uploaded successfully!`);
                    }
                    
                    document.body.removeChild(fileInput);
                });
            });
            
            // Show success message
            alert(`Document "${documentTitle}" deleted successfully!`);
        }
    }
    
    // Upload new document button
    const newDocumentBtn = document.querySelector('.new-document-btn');
    
    newDocumentBtn.addEventListener('click', function() {
        // In a real application, this would open a modal to upload a new document
        // For now, just show an alert
        alert('This would open a modal to upload a new document');
    });
});
