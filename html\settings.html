<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - Student Portal</title>
    <link rel="stylesheet" href="../css/dashboard.css">
    <link rel="stylesheet" href="../css/settings.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <a href="../index.html">Student Portal</a>
            </div>

            <div class="user-profile">
                <div class="avatar">JD</div>
                <div class="user-info">
                    <div class="user-name"><PERSON></div>
                    <div class="user-id">ID: STUD1245</div>
                </div>
            </div>

            <nav class="sidebar-nav">
                <a href="dashboard.html" class="nav-item">
                    <i class="fas fa-home"></i> Dashboard
                </a>
                <a href="profile.html" class="nav-item">
                    <i class="fas fa-user"></i> Profile
                </a>
                <a href="createid.html" class="nav-item">
                    <i class="fas fa-id-card"></i> Create New ID
                </a>
                <a href="documents.html" class="nav-item">
                    <i class="fas fa-file-alt"></i> Documents
                </a>
                <a href="accessibility.html" class="nav-item">
                    <i class="fas fa-universal-access"></i> Display & Accessibility
                </a>
                <a href="settings.html" class="nav-item active">
                    <i class="fas fa-cog"></i> Settings
                </a>
                <a href="../backend/logout.php" class="nav-item logout-btn">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="top-bar">
                <button type="button" class="menu-toggle" title="Toggle Menu">
                    <i class="fas fa-bars"></i>
                </button>

                <div class="search-bar">
                    <input type="text" placeholder="Search...">
                    <i class="fas fa-search"></i>
                </div>

                <div class="top-icons">
                    <div class="icon-badge">
                        <i class="fas fa-bell"></i>
                        <span class="badge">1</span>
                    </div>
                    <div class="icon-badge">
                        <i class="fas fa-envelope"></i>
                        <span class="badge">2</span>
                    </div>
                </div>
            </div>

            <div class="dashboard-content">
                <div class="page-header">
                    <h1>Settings</h1>
                    <p>Manage your account settings and preferences</p>
                </div>

                <div class="settings-container">
                    <div class="settings-sidebar">
                        <div class="settings-nav">
                            <a href="#account" class="settings-nav-item active" data-target="account-settings">
                                <i class="fas fa-user-cog"></i> Account
                            </a>
                            <a href="#security" class="settings-nav-item" data-target="security-settings">
                                <i class="fas fa-shield-alt"></i> Security
                            </a>
                            <a href="#notifications" class="settings-nav-item" data-target="notification-settings">
                                <i class="fas fa-bell"></i> Notifications
                            </a>
                            <a href="#privacy" class="settings-nav-item" data-target="privacy-settings">
                                <i class="fas fa-user-shield"></i> Privacy
                            </a>
                            <a href="#connected" class="settings-nav-item" data-target="connected-accounts">
                                <i class="fas fa-link"></i> Connected Accounts
                            </a>
                        </div>
                    </div>

                    <div class="settings-content">
                        <!-- Account Settings -->
                        <div id="account-settings" class="settings-section active">
                            <div class="section-header">
                                <h2>Account Settings</h2>
                                <p>Manage your account information</p>
                            </div>

                            <form id="accountSettingsForm">
                                <div class="form-group">
                                    <label for="email">Email Address</label>
                                    <input type="email" id="email" value="<EMAIL>">
                                </div>

                                <div class="form-group">
                                    <label for="phone">Phone Number</label>
                                    <input type="tel" id="phone" value="+****************">
                                </div>

                                <div class="form-group">
                                    <label for="language">Preferred Language</label>
                                    <select id="language">
                                        <option value="en" selected>English</option>
                                        <option value="es">Spanish</option>
                                        <option value="fr">French</option>
                                        <option value="de">German</option>
                                        <option value="zh">Chinese</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="timezone">Time Zone</label>
                                    <select id="timezone">
                                        <option value="utc-8" selected>Pacific Time (UTC-8)</option>
                                        <option value="utc-7">Mountain Time (UTC-7)</option>
                                        <option value="utc-6">Central Time (UTC-6)</option>
                                        <option value="utc-5">Eastern Time (UTC-5)</option>
                                        <option value="utc">UTC</option>
                                        <option value="utc+1">Central European Time (UTC+1)</option>
                                    </select>
                                </div>

                                <button type="submit" class="save-button">Save Changes</button>
                            </form>
                        </div>

                        <!-- Security Settings -->
                        <div id="security-settings" class="settings-section">
                            <div class="section-header">
                                <h2>Security Settings</h2>
                                <p>Manage your account security</p>
                            </div>

                            <form id="securitySettingsForm">
                                <div class="form-group">
                                    <label for="current-password">Current Password</label>
                                    <input type="password" id="current-password" placeholder="Enter your current password">
                                </div>

                                <div class="form-group">
                                    <label for="new-password">New Password</label>
                                    <input type="password" id="new-password" placeholder="Enter new password">
                                </div>

                                <div class="form-group">
                                    <label for="confirm-new-password">Confirm New Password</label>
                                    <input type="password" id="confirm-new-password" placeholder="Confirm new password">
                                </div>

                                <div class="form-group toggle-group">
                                    <label class="toggle-label">
                                        <span>Two-Factor Authentication</span>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="twoFactorAuth">
                                            <span class="toggle-slider"></span>
                                        </div>
                                    </label>
                                    <p class="setting-description">Add an extra layer of security to your account</p>
                                </div>

                                <div class="form-group toggle-group">
                                    <label class="toggle-label">
                                        <span>Login Notifications</span>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="loginNotifications" checked>
                                            <span class="toggle-slider"></span>
                                        </div>
                                    </label>
                                    <p class="setting-description">Receive notifications when your account is accessed from a new device</p>
                                </div>

                                <button type="submit" class="save-button">Update Security Settings</button>
                            </form>
                        </div>

                        <!-- Notification Settings -->
                        <div id="notification-settings" class="settings-section">
                            <div class="section-header">
                                <h2>Notification Settings</h2>
                                <p>Manage how you receive notifications</p>
                            </div>

                            <form id="notificationSettingsForm">
                                <div class="form-group toggle-group">
                                    <label class="toggle-label">
                                        <span>Email Notifications</span>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="emailNotifications" checked>
                                            <span class="toggle-slider"></span>
                                        </div>
                                    </label>
                                </div>

                                <div class="form-group toggle-group">
                                    <label class="toggle-label">
                                        <span>SMS Notifications</span>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="smsNotifications">
                                            <span class="toggle-slider"></span>
                                        </div>
                                    </label>
                                </div>

                                <div class="form-group toggle-group">
                                    <label class="toggle-label">
                                        <span>Browser Notifications</span>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="browserNotifications" checked>
                                            <span class="toggle-slider"></span>
                                        </div>
                                    </label>
                                </div>

                                <h3 class="settings-subheader">Notification Types</h3>

                                <div class="form-group checkbox-group">
                                    <input type="checkbox" id="idCardUpdates" checked>
                                    <label for="idCardUpdates">ID Card Application Updates</label>
                                </div>

                                <div class="form-group checkbox-group">
                                    <input type="checkbox" id="documentReminders" checked>
                                    <label for="documentReminders">Document Submission Reminders</label>
                                </div>

                                <div class="form-group checkbox-group">
                                    <input type="checkbox" id="accountActivity" checked>
                                    <label for="accountActivity">Account Activity</label>
                                </div>

                                <div class="form-group checkbox-group">
                                    <input type="checkbox" id="newsUpdates">
                                    <label for="newsUpdates">News and Updates</label>
                                </div>

                                <button type="submit" class="save-button">Save Notification Preferences</button>
                            </form>
                        </div>

                        <!-- Privacy Settings -->
                        <div id="privacy-settings" class="settings-section">
                            <!-- Privacy settings content here -->
                        </div>

                        <!-- Connected Accounts -->
                        <div id="connected-accounts" class="settings-section">
                            <!-- Connected accounts content here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../js/dashboard.js"></script>
    <script src="../js/settings.js"></script>
</body>
</html>
