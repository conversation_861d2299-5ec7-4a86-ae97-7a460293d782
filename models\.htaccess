# Face-API.js Model Server Configuration

# Enable CORS for model loading
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type"

# Set proper MIME types for model files
AddType application/json .json
AddType application/octet-stream .bin
AddType application/octet-stream .shard1
AddType application/octet-stream .shard2

# Enable compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE application/json
    AddOutputFilterByType DEFLATE application/octet-stream
</IfModule>

# Set caching headers for model files
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType application/json "access plus 1 day"
    ExpiresByType application/octet-stream "access plus 1 day"
</IfModule>

# Rewrite rules to route model requests through model-server.php
RewriteEngine On

# Route model file requests to the PHP server
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.+)$ model-server.php?model=$1 [QSA,L]

# Security: Prevent direct access to model directories
<FilesMatch "\.(bin|shard1|shard2)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Allow access through the PHP server
<Files "model-server.php">
    Order Allow,Deny
    Allow from all
</Files>
