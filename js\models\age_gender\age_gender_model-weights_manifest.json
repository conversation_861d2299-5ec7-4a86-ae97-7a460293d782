{"modelTopology": {"class_name": "Model", "config": {"name": "age_gender", "layers": [{"name": "input_1", "class_name": "InputLayer", "config": {"batch_input_shape": [null, 62, 62, 3], "dtype": "float32", "sparse": false, "name": "input_1"}}]}}, "weightsManifest": [{"paths": ["age_gender_model-shard1.bin"], "weights": [{"name": "conv2d_1/kernel", "shape": [3, 3, 3, 32], "dtype": "float32"}, {"name": "conv2d_1/bias", "shape": [32], "dtype": "float32"}, {"name": "age_dense/kernel", "shape": [512, 1], "dtype": "float32"}, {"name": "age_dense/bias", "shape": [1], "dtype": "float32"}, {"name": "gender_dense/kernel", "shape": [512, 2], "dtype": "float32"}, {"name": "gender_dense/bias", "shape": [2], "dtype": "float32"}]}], "format": "layers-model", "generatedBy": "TensorFlow.js tfjs-layers v1.0.0", "convertedBy": "TensorFlow.js Converter v1.0.0"}