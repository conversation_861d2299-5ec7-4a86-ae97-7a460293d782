/**
 * Universal Theme Loader for Student ID Application
 * This script loads and applies the user's selected theme across all pages
 */

(function() {
    'use strict';

    // Load dark theme CSS if not already loaded
    function loadDarkThemeCSS() {
        if (!document.querySelector('link[href*="dark-theme.css"]')) {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = '../css/dark-theme.css';
            document.head.appendChild(link);
        }
    }

    // Apply theme to body
    function applyTheme(theme) {
        document.body.classList.remove('light-theme', 'dark-theme');
        
        if (theme === 'dark') {
            loadDarkThemeCSS();
            document.body.classList.add('dark-theme');
        } else {
            document.body.classList.add('light-theme');
        }
    }

    // Load and apply saved theme immediately
    function initializeTheme() {
        const savedTheme = localStorage.getItem('selectedTheme') || 'light';
        applyTheme(savedTheme);
    }

    // Initialize theme as soon as possible
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeTheme);
    } else {
        initializeTheme();
    }

    // Make theme functions globally available
    window.ThemeLoader = {
        applyTheme: applyTheme,
        getCurrentTheme: function() {
            return localStorage.getItem('selectedTheme') || 'light';
        },
        setTheme: function(theme) {
            localStorage.setItem('selectedTheme', theme);
            applyTheme(theme);
        }
    };
})();
