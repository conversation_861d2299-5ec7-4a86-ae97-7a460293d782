<?php
/**
 * Authentication Handler
 *
 * This file contains functions for user authentication, registration, and session management.
 */

require_once 'config.php';

/**
 * Register a new user
 *
 * @param string $username Username
 * @param string $password Password
 * @param string $email Email address
 * @return array Result of the registration attempt
 */
function registerUser($username, $password, $email) {
    // Validate input
    if (empty($username) || empty($password) || empty($email)) {
        return ['success' => false, 'message' => 'All fields are required'];
    }

    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        return ['success' => false, 'message' => 'Invalid email format'];
    }

    // Validate username (alphanumeric and underscore only)
    if (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
        return ['success' => false, 'message' => 'Username can only contain letters, numbers, and underscores'];
    }

    // Validate password strength
    if (strlen($password) < 8) {
        return ['success' => false, 'message' => 'Password must be at least 8 characters long'];
    }

    try {
        $db = getDbConnection();

        // Check if username already exists
        $stmt = $db->prepare("SELECT user_id FROM users WHERE username = ?");
        $stmt->execute([$username]);
        if ($stmt->rowCount() > 0) {
            return ['success' => false, 'message' => 'Username already exists'];
        }

        // Check if email already exists
        $stmt = $db->prepare("SELECT user_id FROM users WHERE email = ?");
        $stmt->execute([$email]);
        if ($stmt->rowCount() > 0) {
            return ['success' => false, 'message' => 'Email already exists'];
        }

        // Hash the password
        $passwordHash = password_hash($password, PASSWORD_DEFAULT);

        // Insert the new user
        $stmt = $db->prepare("INSERT INTO users (username, password_hash, email) VALUES (?, ?, ?)");
        $stmt->execute([$username, $passwordHash, $email]);

        $userId = $db->lastInsertId();

        // Create empty profile for the user
        $stmt = $db->prepare("INSERT INTO user_profiles (user_id, full_name) VALUES (?, ?)");
        $stmt->execute([$userId, $username]);

        // Create default settings for the user
        $stmt = $db->prepare("INSERT INTO user_settings (user_id) VALUES (?)");
        $stmt->execute([$userId]);

        // Create default notification preferences for the user
        $stmt = $db->prepare("INSERT INTO notification_preferences (user_id) VALUES (?)");
        $stmt->execute([$userId]);

        // Log the registration
        logActivity($userId, 'REGISTER', 'User registered');

        return ['success' => true, 'message' => 'Registration successful', 'user_id' => $userId];
    } catch (PDOException $e) {
        error_log("Registration Error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Registration failed. Please try again later.'];
    }
}

/**
 * Login a user
 *
 * @param string $username Username
 * @param string $password Password
 * @return array Result of the login attempt
 */
function loginUser($username, $password) {
    // Validate input
    if (empty($username) || empty($password)) {
        return ['success' => false, 'message' => 'Username and password are required'];
    }

    try {
        $db = getDbConnection();

        // Get user by username
        $stmt = $db->prepare("SELECT user_id, username, password_hash, is_active FROM users WHERE username = ?");
        $stmt->execute([$username]);
        $user = $stmt->fetch();

        if (!$user) {
            return ['success' => false, 'message' => 'Invalid username or password'];
        }

        // Check if user is active
        if (!$user['is_active']) {
            return ['success' => false, 'message' => 'Account is inactive. Please contact support.'];
        }

        // Verify password
        if (!password_verify($password, $user['password_hash'])) {
            return ['success' => false, 'message' => 'Invalid username or password'];
        }

        // Update last login time
        $stmt = $db->prepare("UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE user_id = ?");
        $stmt->execute([$user['user_id']]);

        // Get user profile
        $stmt = $db->prepare("SELECT full_name, student_id FROM user_profiles WHERE user_id = ?");
        $stmt->execute([$user['user_id']]);
        $profile = $stmt->fetch();

        // Set session variables
        $_SESSION['user_id'] = $user['user_id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['full_name'] = $profile['full_name'] ?? $user['username'];
        $_SESSION['student_id'] = $profile['student_id'] ?? '';
        $_SESSION['logged_in'] = true;
        $_SESSION['login_time'] = time();

        // Log the login
        logActivity($user['user_id'], 'LOGIN', 'User logged in');

        return [
            'success' => true,
            'message' => 'Login successful',
            'user' => [
                'user_id' => $user['user_id'],
                'username' => $user['username'],
                'full_name' => $profile['full_name'] ?? $user['username'],
                'student_id' => $profile['student_id'] ?? ''
            ]
        ];
    } catch (PDOException $e) {
        error_log("Login Error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Login failed. Please try again later.'];
    }
}

/**
 * Logout the current user
 */
function logoutUser() {
    // Log the logout if user is logged in
    if (isset($_SESSION['user_id'])) {
        logActivity($_SESSION['user_id'], 'LOGOUT', 'User logged out');
    }

    // Unset all session variables
    $_SESSION = [];

    // Delete the session cookie
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(
            session_name(),
            '',
            time() - 42000,
            $params["path"],
            $params["domain"],
            $params["secure"],
            $params["httponly"]
        );
    }

    // Destroy the session
    session_destroy();
}

/**
 * Check if user is logged in
 *
 * @return bool True if user is logged in, false otherwise
 */
function isLoggedIn() {
    // Check if user is logged in via session
    if (isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true) {
        // Check if session has expired
        if (isSessionExpired()) {
            // Session expired, check for remember me cookie
            return checkRememberMeCookie();
        }
        return true;
    }

    // Not logged in via session, check for remember me cookie
    return checkRememberMeCookie();
}

/**
 * Check if session has expired
 *
 * @return bool True if session has expired, false otherwise
 */
function isSessionExpired() {
    if (!isset($_SESSION['login_time'])) {
        return true;
    }

    return (time() - $_SESSION['login_time']) > SESSION_LIFETIME;
}

/**
 * Check for remember me cookie and log in user if valid
 *
 * @return bool True if user was logged in via cookie, false otherwise
 */
function checkRememberMeCookie() {
    // Check if remember me cookie exists
    if (!isset($_COOKIE['remember_me'])) {
        return false;
    }

    // Parse the cookie value
    $cookieValue = $_COOKIE['remember_me'];
    $parts = explode(':', $cookieValue);

    if (count($parts) !== 2) {
        // Invalid cookie format
        setcookie('remember_me', '', time() - 3600, '/', '', true, true); // Delete the cookie
        return false;
    }

    $selector = $parts[0];
    $validator = $parts[1];

    try {
        $db = getDbConnection();

        // Get the token from the database
        $stmt = $db->prepare("SELECT user_id, hashed_validator, expires FROM auth_tokens WHERE selector = ?");
        $stmt->execute([$selector]);
        $token = $stmt->fetch();

        if (!$token) {
            // Token not found
            setcookie('remember_me', '', time() - 3600, '/', '', true, true); // Delete the cookie
            return false;
        }

        // Check if token has expired
        if (strtotime($token['expires']) < time()) {
            // Token expired
            $stmt = $db->prepare("DELETE FROM auth_tokens WHERE selector = ?");
            $stmt->execute([$selector]);
            setcookie('remember_me', '', time() - 3600, '/', '', true, true); // Delete the cookie
            return false;
        }

        // Verify the validator
        if (!password_verify($validator, $token['hashed_validator'])) {
            // Invalid validator
            setcookie('remember_me', '', time() - 3600, '/', '', true, true); // Delete the cookie
            return false;
        }

        // Get user data
        $stmt = $db->prepare("SELECT u.user_id, u.username, p.full_name, p.student_id
                             FROM users u
                             LEFT JOIN user_profiles p ON u.user_id = p.user_id
                             WHERE u.user_id = ?");
        $stmt->execute([$token['user_id']]);
        $user = $stmt->fetch();

        if (!$user) {
            // User not found
            setcookie('remember_me', '', time() - 3600, '/', '', true, true); // Delete the cookie
            return false;
        }

        // Set session variables
        $_SESSION['user_id'] = $user['user_id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['full_name'] = $user['full_name'] ?? $user['username'];
        $_SESSION['student_id'] = $user['student_id'] ?? '';
        $_SESSION['logged_in'] = true;
        $_SESSION['login_time'] = time();

        // Regenerate the token for better security
        $newSelector = bin2hex(random_bytes(16));
        $newValidator = bin2hex(random_bytes(32));
        $hashedValidator = password_hash($newValidator, PASSWORD_DEFAULT);
        $expires = date('Y-m-d H:i:s', time() + 30 * 24 * 60 * 60); // 30 days

        // Update the token in the database
        $stmt = $db->prepare("UPDATE auth_tokens SET selector = ?, hashed_validator = ?, expires = ? WHERE selector = ?");
        $stmt->execute([$newSelector, $hashedValidator, $expires, $selector]);

        // Set a new cookie
        $cookieValue = $newSelector . ':' . $newValidator;
        setcookie(
            'remember_me',
            $cookieValue,
            time() + 30 * 24 * 60 * 60, // 30 days
            '/',
            '',
            true, // Secure
            true  // HttpOnly
        );

        // Log the auto-login
        logActivity($user['user_id'], 'AUTO_LOGIN', 'User logged in via remember me cookie');

        return true;
    } catch (PDOException $e) {
        error_log("Remember Me Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Log user activity
 *
 * @param int $userId User ID
 * @param string $action Action performed
 * @param string $details Additional details
 */
function logActivity($userId, $action, $details = '') {
    try {
        $db = getDbConnection();

        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

        $stmt = $db->prepare("INSERT INTO activity_logs (user_id, action, details, ip_address, user_agent) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$userId, $action, $details, $ipAddress, $userAgent]);
    } catch (PDOException $e) {
        error_log("Activity Log Error: " . $e->getMessage());
    }
}
