<?php
/**
 * Documents Page
 *
 * This is the page for managing student documents.
 */

require_once '../backend/config.php';
require_once '../backend/auth.php';

// Redirect to login if not authenticated
if (!isLoggedIn()) {
    header('Location: ../index.php');
    exit;
}

// Get user data from session
$userId = $_SESSION['user_id'];
$username = $_SESSION['username'];
$fullName = $_SESSION['full_name'];
$studentId = $_SESSION['student_id'];

// Get user documents
try {
    $db = getDbConnection();

    // Get user documents
    $stmt = $db->prepare("
        SELECT * FROM user_documents
        WHERE user_id = ?
        ORDER BY document_type, upload_date DESC
    ");
    $stmt->execute([$userId]);
    $documents = $stmt->fetchAll();

    // Group documents by type
    $documentsByType = [];
    foreach ($documents as $doc) {
        $documentsByType[$doc['document_type']][] = $doc;
    }

} catch (PDOException $e) {
    error_log("Documents Error: " . $e->getMessage());
    $documents = [];
    $documentsByType = [];
}

// Get user initials for avatar
$initials = '';
if ($fullName) {
    $nameParts = explode(' ', $fullName);
    if (count($nameParts) >= 2) {
        $initials = substr($nameParts[0], 0, 1) . substr($nameParts[1], 0, 1);
    } else {
        $initials = substr($fullName, 0, 2);
    }
} else {
    $initials = substr($username, 0, 2);
}
$initials = strtoupper($initials);

// Generate CSRF token if it doesn't exist
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Helper function to check if a document type exists
function hasDocument($documentsByType, $type) {
    return isset($documentsByType[$type]) && !empty($documentsByType[$type]);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documents - Student Portal</title>
    <link rel="stylesheet" href="../css/dashboard.css">
    <link rel="stylesheet" href="../css/documents.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="../js/theme-loader.js"></script>
</head>
<body>
    <div class="dashboard-container">
        <?php
        // Set current page for sidebar highlighting
        $currentPage = 'documents';

        // Include sidebar template
        include 'sidebar-template.php';
        ?>

        <!-- Main Content -->
        <div class="main-content">
            <div class="top-bar">
                <button type="button" class="menu-toggle" title="Toggle Menu">
                    <i class="fas fa-bars"></i>
                </button>

                <div class="top-icons">
                    <div class="icon-badge">
                        <i class="fas fa-bell"></i>
                        <span class="badge">1</span>
                    </div>
                    <div class="icon-badge">
                        <i class="fas fa-envelope"></i>
                        <span class="badge">2</span>
                    </div>
                </div>
            </div>

            <div class="dashboard-content">
                <div class="page-header">
                    <h1>Documents</h1>
                    <p>Manage your important documents and verification files</p>
                </div>

                <div class="documents-container">
                    <div class="documents-section">
                        <div class="section-header">
                            <h2>Required Documents</h2>
                            <p>These documents are required for your student ID application</p>
                        </div>

                        <div class="document-list">
                            <div class="document-item">
                                <div class="document-icon">
                                    <i class="fas fa-id-card"></i>
                                </div>
                                <div class="document-details">
                                    <h3>Government-Issued ID</h3>
                                    <p>Upload a copy of your passport, driver's license, or national ID card</p>
                                    <?php if (hasDocument($documentsByType, 'GOVERNMENT_ID')): ?>
                                    <div class="document-status complete">
                                        <i class="fas fa-check-circle"></i> Uploaded
                                    </div>
                                    <?php else: ?>
                                    <div class="document-status incomplete">
                                        <i class="fas fa-exclamation-circle"></i> Required
                                    </div>
                                    <?php endif; ?>
                                </div>
                                <div class="document-actions">
                                    <?php if (hasDocument($documentsByType, 'GOVERNMENT_ID')): ?>
                                    <button class="view-btn" data-document-id="<?php echo $documentsByType['GOVERNMENT_ID'][0]['document_id']; ?>">
                                        <i class="fas fa-eye"></i> View
                                    </button>
                                    <button type="button" class="delete-btn" title="Delete Document" data-document-id="<?php echo $documentsByType['GOVERNMENT_ID'][0]['document_id']; ?>">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <?php else: ?>
                                    <button class="upload-btn" data-document-type="GOVERNMENT_ID">
                                        <i class="fas fa-upload"></i> Upload
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="document-item">
                                <div class="document-icon">
                                    <i class="fas fa-graduation-cap"></i>
                                </div>
                                <div class="document-details">
                                    <h3>Enrollment Verification</h3>
                                    <p>Proof of enrollment or acceptance letter from your department</p>
                                    <?php if (hasDocument($documentsByType, 'ENROLLMENT')): ?>
                                    <div class="document-status complete">
                                        <i class="fas fa-check-circle"></i> Uploaded
                                    </div>
                                    <?php else: ?>
                                    <div class="document-status incomplete">
                                        <i class="fas fa-exclamation-circle"></i> Required
                                    </div>
                                    <?php endif; ?>
                                </div>
                                <div class="document-actions">
                                    <?php if (hasDocument($documentsByType, 'ENROLLMENT')): ?>
                                    <button class="view-btn" data-document-id="<?php echo $documentsByType['ENROLLMENT'][0]['document_id']; ?>">
                                        <i class="fas fa-eye"></i> View
                                    </button>
                                    <button type="button" class="delete-btn" title="Delete Document" data-document-id="<?php echo $documentsByType['ENROLLMENT'][0]['document_id']; ?>">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <?php else: ?>
                                    <button class="upload-btn" data-document-type="ENROLLMENT">
                                        <i class="fas fa-upload"></i> Upload
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="document-item">
                                <div class="document-icon">
                                    <i class="fas fa-file-invoice"></i>
                                </div>
                                <div class="document-details">
                                    <h3>Tuition Receipt</h3>
                                    <p>Proof of payment for the current semester</p>
                                    <?php if (hasDocument($documentsByType, 'TUITION')): ?>
                                    <div class="document-status complete">
                                        <i class="fas fa-check-circle"></i> Uploaded
                                    </div>
                                    <?php else: ?>
                                    <div class="document-status incomplete">
                                        <i class="fas fa-exclamation-circle"></i> Required
                                    </div>
                                    <?php endif; ?>
                                </div>
                                <div class="document-actions">
                                    <?php if (hasDocument($documentsByType, 'TUITION')): ?>
                                    <button class="view-btn" data-document-id="<?php echo $documentsByType['TUITION'][0]['document_id']; ?>">
                                        <i class="fas fa-eye"></i> View
                                    </button>
                                    <button type="button" class="delete-btn" title="Delete Document" data-document-id="<?php echo $documentsByType['TUITION'][0]['document_id']; ?>">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <?php else: ?>
                                    <button class="upload-btn" data-document-type="TUITION">
                                        <i class="fas fa-upload"></i> Upload
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="documents-section">
                        <div class="section-header">
                            <h2>Additional Documents</h2>
                            <p>Optional documents that may be required for specific services</p>
                        </div>

                        <div class="document-list">
                            <div class="document-item">
                                <div class="document-icon">
                                    <i class="fas fa-heartbeat"></i>
                                </div>
                                <div class="document-details">
                                    <h3>Medical Insurance</h3>
                                    <p>Proof of medical insurance coverage</p>
                                    <?php if (hasDocument($documentsByType, 'MEDICAL')): ?>
                                    <div class="document-status complete">
                                        <i class="fas fa-check-circle"></i> Uploaded
                                    </div>
                                    <?php else: ?>
                                    <div class="document-status optional">
                                        <i class="fas fa-info-circle"></i> Optional
                                    </div>
                                    <?php endif; ?>
                                </div>
                                <div class="document-actions">
                                    <?php if (hasDocument($documentsByType, 'MEDICAL')): ?>
                                    <button class="view-btn" data-document-id="<?php echo $documentsByType['MEDICAL'][0]['document_id']; ?>">
                                        <i class="fas fa-eye"></i> View
                                    </button>
                                    <button type="button" class="delete-btn" title="Delete Document" data-document-id="<?php echo $documentsByType['MEDICAL'][0]['document_id']; ?>">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <?php else: ?>
                                    <button class="upload-btn" data-document-type="MEDICAL">
                                        <i class="fas fa-upload"></i> Upload
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="document-item">
                                <div class="document-icon">
                                    <i class="fas fa-home"></i>
                                </div>
                                <div class="document-details">
                                    <h3>Housing Contract</h3>
                                    <p>On-campus housing agreement or lease</p>
                                    <?php if (hasDocument($documentsByType, 'HOUSING')): ?>
                                    <div class="document-status complete">
                                        <i class="fas fa-check-circle"></i> Uploaded
                                    </div>
                                    <?php else: ?>
                                    <div class="document-status optional">
                                        <i class="fas fa-info-circle"></i> Optional
                                    </div>
                                    <?php endif; ?>
                                </div>
                                <div class="document-actions">
                                    <?php if (hasDocument($documentsByType, 'HOUSING')): ?>
                                    <button class="view-btn" data-document-id="<?php echo $documentsByType['HOUSING'][0]['document_id']; ?>">
                                        <i class="fas fa-eye"></i> View
                                    </button>
                                    <button type="button" class="delete-btn" title="Delete Document" data-document-id="<?php echo $documentsByType['HOUSING'][0]['document_id']; ?>">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <?php else: ?>
                                    <button class="upload-btn" data-document-type="HOUSING">
                                        <i class="fas fa-upload"></i> Upload
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="upload-new-document">
                        <button class="new-document-btn" id="uploadNewDocument">
                            <i class="fas fa-plus"></i> Upload New Document
                        </button>
                    </div>

                    <!-- Upload Document Form (Hidden by default) -->
                    <div class="upload-form-container hidden" id="uploadFormContainer">
                        <form id="documentUploadForm" action="../backend/upload_document.php" method="post" enctype="multipart/form-data">
                            <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                            <h3>Upload Document</h3>

                            <div class="form-group">
                                <label for="documentType">Document Type</label>
                                <select id="documentType" name="document_type" required>
                                    <option value="" disabled selected>Select document type</option>
                                    <option value="GOVERNMENT_ID">Government-Issued ID</option>
                                    <option value="ENROLLMENT">Enrollment Verification</option>
                                    <option value="TUITION">Tuition Receipt</option>
                                    <option value="MEDICAL">Medical Insurance</option>
                                    <option value="HOUSING">Housing Contract</option>
                                    <option value="OTHER">Other Document</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="documentFile">Select File</label>
                                <input type="file" id="documentFile" name="document_file" required>
                                <p class="file-info">PDF, JPG, PNG or GIF. Max size 5MB.</p>
                            </div>

                            <div class="form-group">
                                <label for="documentDescription">Description (Optional)</label>
                                <textarea id="documentDescription" name="description" rows="3" placeholder="Enter a brief description of this document"></textarea>
                            </div>

                            <div class="form-actions">
                                <button type="button" id="cancelUpload" class="cancel-btn">Cancel</button>
                                <button type="submit" class="submit-btn">Upload Document</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <?php include 'footer-template.php'; ?>
        </div>
    </div>

    <script src="../js/dashboard.js"></script>
    <script src="../js/documents.js"></script>
</body>
</html>
