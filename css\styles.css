* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Arial', sans-serif;
}

body {
    background-color: #f0e6ff;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    justify-content: center;
    align-items: center;
}

.container {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 450px;
    padding: 40px;
    margin: 20px;
}

h1 {
    text-align: center;
    font-size: 28px;
    margin-bottom: 10px;
}

.subtitle {
    text-align: center;
    color: #666;
    font-size: 18px;
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
}

input {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
}

.btn-primary {
    width: 100%;
    padding: 14px;
    background-color: #8a3df9;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    margin-top: 10px;
    margin-bottom: 20px;
}

.btn-primary:hover {
    background-color: #7a2df9;
}

.switch-form {
    text-align: center;
    margin-bottom: 15px;
}

.terms {
    text-align: center;
    font-size: 14px;
    color: #666;
    margin-top: 20px;
}

a {
    color: #8a3df9;
    text-decoration: none;
    position: relative; /* Ensure links have a stacking context */
    z-index: 10; /* Higher z-index to ensure clickability */
    cursor: pointer;
}

a:hover {
    text-decoration: underline;
}

/* Styling for Terms of Service button */
.terms-button {
    display: inline-block;
    background-color: #5e259b;
    color: white !important;
    padding: 5px 10px;
    border-radius: 4px;
    margin: 0 5px;
    text-decoration: none !important;
    font-weight: 500;
    transition: background-color 0.3s;
}

.terms-button:hover {
    background-color: #8a3df9;
    text-decoration: none !important;
}

.forgot-password {
    text-align: center;
    margin-top: 15px;
}

footer {
    margin-top: 20px;
    color: #666;
    text-align: center;
    padding: 10px;
}

.hidden {
    display: none;
}

.error-message {
    background-color: #ffebee;
    color: #d32f2f;
    border: 1px solid #f5c6cb;
    border-radius: 5px;
    padding: 10px;
    margin-bottom: 20px;
    text-align: center;
}

.success-message {
    background-color: #e8f5e9;
    color: #2e7d32;
    border: 1px solid #c8e6c9;
    border-radius: 5px;
    padding: 10px;
    margin-bottom: 20px;
    text-align: center;
}

.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
    margin-right: 8px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.remember-me {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.remember-me input {
    width: auto;
    margin-right: 8px;
}

.remember-me label {
    display: inline;
    font-weight: normal;
}

.password-hint {
    display: block;
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}
