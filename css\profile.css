/* Profile Page Specific Styles */
.profile-header {
    margin-bottom: 30px;
}

.profile-header h1 {
    font-size: 24px;
    margin-bottom: 5px;
}

.profile-header p {
    color: #666;
}

.profile-content {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.profile-section {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    flex: 1;
    min-width: 300px;
}

.section-header {
    margin-bottom: 20px;
}

.section-header h2 {
    font-size: 18px;
    margin-bottom: 5px;
}

.section-header p {
    color: #666;
    font-size: 14px;
}

/* Form Styling */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group input[type="text"] {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
}

/* Profile Image Styling */
.profile-image-container {
    display: flex;
    align-items: center;
    gap: 20px;
}

.profile-image {
    width: 80px;
    height: 80px;
    background-color: #f0e6ff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #8a3df9;
    font-size: 30px;
}

.upload-button {
    display: flex;
    flex-direction: column;
}

.upload-label {
    display: inline-block;
    padding: 8px 15px;
    background-color: #f0e6ff;
    color: #8a3df9;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    margin-bottom: 5px;
}

.upload-label:hover {
    background-color: #e5d5ff;
}

.file-info {
    font-size: 12px;
    color: #888;
}

/* Save Button */
.save-button {
    background-color: #8a3df9;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 12px 20px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.save-button:hover {
    background-color: #7a2df9;
}

/* Alert Messages */
.alert {
    padding: 12px 15px;
    border-radius: 5px;
    margin-top: 15px;
    font-size: 14px;
}

.alert-success {
    background-color: #e6f7e6;
    color: #28a745;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background-color: #f8d7da;
    color: #dc3545;
    border: 1px solid #f5c6cb;
}
