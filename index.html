<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Account</title>
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <div class="container">
        <!-- Error/Success Message Display -->
        <div id="error-message" class="error-message hidden"></div>

        <!-- Signup Form -->
        <div class="form-container" id="signup-form">
            <h1>Welcome,</h1>
            <p class="subtitle">Create your student account</p>

            <form action="backend/login.php" method="post">
                <input type="hidden" name="form_type" value="signup">
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token'] ?? ''; ?>">

                <div class="form-group">
                    <label for="username">Username</label>
                    <input type="text" id="username" name="username" placeholder="Enter your username" required>
                </div>

                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" placeholder="Enter your email" required>
                </div>

                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" placeholder="••••••••" required>
                    <small class="password-hint">At least 8 characters with uppercase, lowercase, number, and special character</small>
                </div>

                <div class="form-group">
                    <label for="confirm-password">Confirm Password</label>
                    <input type="password" id="confirm-password" name="confirm_password" placeholder="••••••••" required>
                </div>

                <button type="submit" class="btn-primary">Sign Up</button>
            </form>

            <p class="switch-form">Already have an account? <a href="#" id="show-login">Log in</a></p>

            <p class="terms">By signing up, you agree to our <a href="#">Terms of Service</a> and <a href="#">Privacy Policy</a></p>
        </div>

        <!-- Login Form -->
        <div class="form-container hidden" id="login-form">
            <h1>Welcome back,</h1>
            <p class="subtitle">Log in to your student account</p>

            <form action="backend/login.php" method="post">
                <input type="hidden" name="form_type" value="login">
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token'] ?? ''; ?>">

                <div class="form-group">
                    <label for="login-username">Username</label>
                    <input type="text" id="login-username" name="username" placeholder="Enter your username" required>
                </div>

                <div class="form-group">
                    <label for="login-password">Password</label>
                    <input type="password" id="login-password" name="password" placeholder="••••••••" required>
                </div>

                <div class="remember-me">
                    <input type="checkbox" id="remember-me" name="remember_me">
                    <label for="remember-me">Remember me</label>
                </div>

                <button type="submit" class="btn-primary">Log In</button>
            </form>

            <p class="switch-form">Don't have an account? <a href="#" id="show-signup">Sign up</a></p>

            <p class="forgot-password"><a href="backend/reset_password.php">Forgot password?</a></p>
        </div>
    </div>

    <footer>
        <p>We respect your privacy. All information is encrypted and secure.</p>
    </footer>

    <script src="js/script.js"></script>
</body>
</html>
