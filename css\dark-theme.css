/* Dark Theme Styles for Student ID Application */

/* Apply dark theme when body has dark-theme class */
body.dark-theme {
    background-color: #1a1a1a;
    color: #ffffff;
}

/* Main content areas */
body.dark-theme .main-content {
    background-color: #1a1a1a;
    color: #ffffff;
}

body.dark-theme .dashboard-content {
    background-color: #1a1a1a;
}

/* Cards and sections */
body.dark-theme .profile-section,
body.dark-theme .accessibility-section,
body.dark-theme .settings-section,
body.dark-theme .dashboard-card,
body.dark-theme .document-card,
body.dark-theme .id-card-preview {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 1px solid #404040;
}

/* Headers and text */
body.dark-theme h1,
body.dark-theme h2,
body.dark-theme h3,
body.dark-theme h4,
body.dark-theme h5,
body.dark-theme h6 {
    color: #ffffff;
}

body.dark-theme p,
body.dark-theme span,
body.dark-theme label {
    color: #e0e0e0;
}

body.dark-theme .page-header p,
body.dark-theme .section-header p,
body.dark-theme .setting-description {
    color: #b0b0b0;
}

/* Form elements */
body.dark-theme input[type="text"],
body.dark-theme input[type="email"],
body.dark-theme input[type="password"],
body.dark-theme input[type="tel"],
body.dark-theme input[type="date"],
body.dark-theme select,
body.dark-theme textarea {
    background-color: #404040;
    border: 1px solid #606060;
    color: #ffffff;
}

body.dark-theme input[type="text"]:focus,
body.dark-theme input[type="email"]:focus,
body.dark-theme input[type="password"]:focus,
body.dark-theme input[type="tel"]:focus,
body.dark-theme input[type="date"]:focus,
body.dark-theme select:focus,
body.dark-theme textarea:focus {
    border-color: #8a3df9;
    outline: none;
    box-shadow: 0 0 0 2px rgba(138, 61, 249, 0.2);
}

/* Buttons - preserve purple theme */
body.dark-theme .save-button,
body.dark-theme .btn-primary,
body.dark-theme .upload-label {
    background-color: #8a3df9;
    color: white;
    border: 1px solid #8a3df9;
}

body.dark-theme .save-button:hover,
body.dark-theme .btn-primary:hover,
body.dark-theme .upload-label:hover {
    background-color: #7a2df9;
}

body.dark-theme .btn-secondary {
    background-color: #404040;
    color: #ffffff;
    border: 1px solid #606060;
}

body.dark-theme .btn-secondary:hover {
    background-color: #505050;
}

/* Top bar */
body.dark-theme .top-bar {
    background-color: #2d2d2d;
    border-bottom: 1px solid #404040;
}

body.dark-theme .menu-toggle {
    background-color: transparent;
    color: #ffffff;
    border: 1px solid #606060;
}

body.dark-theme .menu-toggle:hover {
    background-color: #404040;
}

body.dark-theme .top-icons .icon-badge {
    color: #ffffff;
}

body.dark-theme .badge {
    background-color: #8a3df9;
    color: white;
}

/* Tables */
body.dark-theme table {
    background-color: #2d2d2d;
    color: #ffffff;
}

body.dark-theme th {
    background-color: #404040;
    color: #ffffff;
    border-bottom: 1px solid #606060;
}

body.dark-theme td {
    border-bottom: 1px solid #404040;
    color: #e0e0e0;
}

body.dark-theme tr:hover {
    background-color: #353535;
}

/* Alerts */
body.dark-theme .alert-success {
    background-color: #1a4d1a;
    color: #90ee90;
    border: 1px solid #2d6a2d;
}

body.dark-theme .alert-error {
    background-color: #4d1a1a;
    color: #ff9999;
    border: 1px solid #6a2d2d;
}

body.dark-theme .alert-warning {
    background-color: #4d4d1a;
    color: #ffff99;
    border: 1px solid #6a6a2d;
}

body.dark-theme .alert-info {
    background-color: #1a1a4d;
    color: #9999ff;
    border: 1px solid #2d2d6a;
}

/* Profile image container */
body.dark-theme .profile-image {
    background-color: #404040;
    color: #8a3df9;
}

/* Theme selection */
body.dark-theme .theme-label {
    border: 2px solid #404040;
}

body.dark-theme input[type="radio"]:checked + .theme-label {
    border-color: #8a3df9;
    background-color: #2d1a4d;
}

/* Toggle switches - preserve purple theme */
body.dark-theme .toggle-slider {
    background-color: #606060;
}

body.dark-theme input:checked + .toggle-slider {
    background-color: #8a3df9;
}

/* Range sliders - preserve purple theme */
body.dark-theme .slider {
    background: #606060;
}

body.dark-theme .slider::-webkit-slider-thumb {
    background: #8a3df9;
}

body.dark-theme .slider::-moz-range-thumb {
    background: #8a3df9;
}

body.dark-theme .range-value {
    color: #8a3df9;
}

/* Footer */
body.dark-theme footer {
    background-color: #2d2d2d;
    color: #e0e0e0;
    border-top: 1px solid #404040;
}

body.dark-theme footer a {
    color: #8a3df9;
}

body.dark-theme footer a:hover {
    color: #9a4dff;
}

/* Scrollbars */
body.dark-theme ::-webkit-scrollbar {
    width: 8px;
}

body.dark-theme ::-webkit-scrollbar-track {
    background: #2d2d2d;
}

body.dark-theme ::-webkit-scrollbar-thumb {
    background: #606060;
    border-radius: 4px;
}

body.dark-theme ::-webkit-scrollbar-thumb:hover {
    background: #707070;
}

/* Document upload areas */
body.dark-theme .upload-area {
    background-color: #2d2d2d;
    border: 2px dashed #606060;
    color: #e0e0e0;
}

body.dark-theme .upload-area:hover {
    border-color: #8a3df9;
    background-color: #353535;
}

/* Status indicators */
body.dark-theme .status-pending {
    background-color: #4d4d1a;
    color: #ffff99;
}

body.dark-theme .status-approved {
    background-color: #1a4d1a;
    color: #90ee90;
}

body.dark-theme .status-rejected {
    background-color: #4d1a1a;
    color: #ff9999;
}

/* Navigation breadcrumbs */
body.dark-theme .breadcrumb {
    background-color: #2d2d2d;
    color: #e0e0e0;
}

body.dark-theme .breadcrumb a {
    color: #8a3df9;
}

/* Modal dialogs */
body.dark-theme .modal {
    background-color: rgba(0, 0, 0, 0.8);
}

body.dark-theme .modal-content {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 1px solid #404040;
}

body.dark-theme .modal-header {
    border-bottom: 1px solid #404040;
}

body.dark-theme .modal-footer {
    border-top: 1px solid #404040;
}

/* Dropdown menus */
body.dark-theme .dropdown-menu {
    background-color: #2d2d2d;
    border: 1px solid #404040;
}

body.dark-theme .dropdown-item {
    color: #e0e0e0;
}

body.dark-theme .dropdown-item:hover {
    background-color: #404040;
    color: #ffffff;
}
