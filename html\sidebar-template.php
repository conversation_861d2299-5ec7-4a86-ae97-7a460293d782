<?php
/**
 * Sidebar Template
 * 
 * This file contains the sidebar template that is included in all pages.
 */

// Get user profile data to check for profile image
try {
    $db = getDbConnection();
    $stmt = $db->prepare("SELECT profile_image FROM user_profiles WHERE user_id = ?");
    $stmt->execute([$userId]);
    $userProfile = $stmt->fetch();
} catch (PDOException $e) {
    error_log("Profile Image Error: " . $e->getMessage());
    $userProfile = null;
}
?>
<!-- Sidebar -->
<div class="sidebar">
    <div class="logo">
        <a href="../index.php">Student Portal</a>
    </div>

    <div class="profile-image-box">
        <?php if (isset($userProfile['profile_image']) && !empty($userProfile['profile_image'])): ?>
            <img src="<?php echo htmlspecialchars($userProfile['profile_image']); ?>" alt="Profile Image">
        <?php else: ?>
            <div class="initials-placeholder"><?php echo $initials; ?></div>
        <?php endif; ?>
    </div>

    <div class="user-info-container">
        <div class="user-name"><?php echo htmlspecialchars($fullName); ?></div>
        <div class="user-id"><?php echo htmlspecialchars($studentId); ?></div>
    </div>

    <nav class="sidebar-nav">
        <a href="dashboard.php" class="nav-item <?php echo $currentPage === 'dashboard' ? 'active' : ''; ?>">
            <i class="fas fa-home"></i> Dashboard
        </a>
        <a href="profile.php" class="nav-item <?php echo $currentPage === 'profile' ? 'active' : ''; ?>">
            <i class="fas fa-user"></i> Profile
        </a>
        <a href="createid.php" class="nav-item <?php echo $currentPage === 'createid' ? 'active' : ''; ?>">
            <i class="fas fa-id-card"></i> Create New ID
        </a>
        <a href="documents.php" class="nav-item <?php echo $currentPage === 'documents' ? 'active' : ''; ?>">
            <i class="fas fa-file-alt"></i> Documents
        </a>
        <a href="accessibility.php" class="nav-item <?php echo $currentPage === 'accessibility' ? 'active' : ''; ?>">
            <i class="fas fa-universal-access"></i> Display & Accessibility
        </a>
        <a href="settings.php" class="nav-item <?php echo $currentPage === 'settings' ? 'active' : ''; ?>">
            <i class="fas fa-cog"></i> Settings
        </a>
        <a href="../backend/logout.php" class="nav-item logout-btn">
            <i class="fas fa-sign-out-alt"></i> Logout
        </a>
    </nav>
</div>
