<?php
/**
 * Accessibility Page
 *
 * This is the accessibility settings page for authenticated users.
 */

require_once '../backend/config.php';
require_once '../backend/auth.php';

// Redirect to login if not authenticated
if (!isLoggedIn()) {
    header('Location: ../index.php');
    exit;
}

// Get user data from session
$userId = $_SESSION['user_id'];
$username = $_SESSION['username'];
$fullName = $_SESSION['full_name'];
$studentId = $_SESSION['student_id'];

// Get user accessibility settings
try {
    $db = getDbConnection();

    // Get user accessibility settings
    $stmt = $db->prepare("
        SELECT * FROM user_settings
        WHERE user_id = ?
    ");
    $stmt->execute([$userId]);
    $accessibility = $stmt->fetch();

    if (!$accessibility) {
        // Default settings if none exist
        $accessibility = [
            'theme' => 'light',
            'font_size' => 100,
            'reduce_animations' => 0,
            'high_contrast' => 0,
            'font_family' => 'default',
            'line_spacing' => 150,
            'screen_reader' => 0,
            'keyboard_nav' => 1,
            'autoplay_videos' => 0,
            'image_descriptions' => 1,
            'focus_mode' => 0
        ];
    }

} catch (PDOException $e) {
    error_log("Accessibility Error: " . $e->getMessage());
    // Default settings if database error
    $accessibility = [
        'theme' => 'light',
        'font_size' => 100,
        'reduce_animations' => 0,
        'high_contrast' => 0,
        'font_family' => 'default',
        'line_spacing' => 150,
        'screen_reader' => 0,
        'keyboard_nav' => 1,
        'autoplay_videos' => 0,
        'image_descriptions' => 1,
        'focus_mode' => 0
    ];
}

// Get user initials for avatar
$initials = '';
if ($fullName) {
    $nameParts = explode(' ', $fullName);
    if (count($nameParts) >= 2) {
        $initials = substr($nameParts[0], 0, 1) . substr($nameParts[1], 0, 1);
    } else {
        $initials = substr($fullName, 0, 2);
    }
} else {
    $initials = substr($username, 0, 2);
}
$initials = strtoupper($initials);

// Generate CSRF token if it doesn't exist
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Display & Accessibility - Student Portal</title>
    <link rel="stylesheet" href="../css/dashboard.css">
    <link rel="stylesheet" href="../css/accessibility.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="../js/theme-loader.js"></script>
</head>
<body>
    <div class="dashboard-container">
        <?php
        // Set current page for sidebar highlighting
        $currentPage = 'accessibility';

        // Include sidebar template
        include 'sidebar-template.php';
        ?>

        <!-- Main Content -->
        <div class="main-content">
            <div class="top-bar">
                <button type="button" class="menu-toggle" title="Toggle Menu">
                    <i class="fas fa-bars"></i>
                </button>

                <div class="top-icons">
                    <div class="icon-badge">
                        <i class="fas fa-bell"></i>
                        <span class="badge">1</span>
                    </div>
                    <div class="icon-badge">
                        <i class="fas fa-envelope"></i>
                        <span class="badge">2</span>
                    </div>
                </div>
            </div>

            <div class="dashboard-content">
                <div class="page-header">
                    <h1>Display & Accessibility</h1>
                    <p>Customize your experience to suit your needs</p>
                </div>

                <div class="accessibility-container">
                    <div class="accessibility-section">
                        <div class="section-header">
                            <h2>Display Preferences</h2>
                            <p>Adjust how content appears on your screen</p>
                        </div>

                        <form id="displayPreferencesForm" action="../backend/update_accessibility.php" method="post">
                            <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                            <input type="hidden" name="settings_type" value="display">

                            <div class="form-group">
                                <label for="theme">Color Theme</label>
                                <div class="theme-options">
                                    <div class="theme-option">
                                        <input type="radio" name="theme" id="theme-light" value="light" <?php echo ($accessibility['theme'] == 'light') ? 'checked' : ''; ?>>
                                        <label for="theme-light" class="theme-label light-theme">
                                            <div class="theme-preview"></div>
                                            <span>Light</span>
                                        </label>
                                    </div>

                                    <div class="theme-option">
                                        <input type="radio" name="theme" id="theme-dark" value="dark" <?php echo ($accessibility['theme'] == 'dark') ? 'checked' : ''; ?>>
                                        <label for="theme-dark" class="theme-label dark-theme">
                                            <div class="theme-preview"></div>
                                            <span>Dark</span>
                                        </label>
                                    </div>


                                </div>
                            </div>

                            <div class="form-group">
                                <label for="fontSize">Text Size</label>
                                <div class="range-slider">
                                    <input type="range" id="fontSize" name="font_size" min="80" max="150" value="<?php echo $accessibility['font_size']; ?>" class="slider">
                                    <div class="range-labels">
                                        <span>A</span>
                                        <span>A</span>
                                        <span>A</span>
                                    </div>
                                    <div class="range-value"><?php echo $accessibility['font_size']; ?>%</div>
                                </div>
                            </div>

                            <div class="form-group toggle-group">
                                <label class="toggle-label">
                                    <span>Reduce Animations</span>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="reduceAnimations" name="reduce_animations" <?php echo ($accessibility['reduce_animations'] == 1) ? 'checked' : ''; ?>>
                                        <span class="toggle-slider"></span>
                                    </div>
                                </label>
                                <p class="setting-description">Minimize motion effects throughout the interface</p>
                            </div>

                            <div class="form-group toggle-group">
                                <label class="toggle-label">
                                    <span>High Contrast Mode</span>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="highContrast" name="high_contrast" <?php echo ($accessibility['high_contrast'] == 1) ? 'checked' : ''; ?>>
                                        <span class="toggle-slider"></span>
                                    </div>
                                </label>
                                <p class="setting-description">Increase contrast for better visibility</p>
                            </div>

                            <button type="submit" class="save-button">Save Display Preferences</button>
                        </form>
                    </div>




                </div>
            </div>

            <?php include 'footer-template.php'; ?>
        </div>
    </div>

    <script src="../js/dashboard.js"></script>
    <script src="../js/accessibility.js"></script>
</body>
</html>
