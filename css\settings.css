/* Settings Page Styles */
.page-header {
    margin-bottom: 30px;
}

.page-header h1 {
    font-size: 24px;
    margin-bottom: 5px;
}

.page-header p {
    color: #666;
}

.settings-container {
    display: flex;
    gap: 30px;
}

.settings-sidebar {
    width: 250px;
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.settings-content {
    flex: 1;
    background-color: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.settings-nav {
    display: flex;
    flex-direction: column;
}

.settings-nav-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    color: #333;
    text-decoration: none;
    border-radius: 5px;
    margin-bottom: 5px;
    transition: all 0.3s;
}

.settings-nav-item i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
    color: #5e259b;
}

.settings-nav-item.active {
    background-color: #f0e6ff;
    color: #5e259b;
    font-weight: 500;
}

.settings-nav-item:hover:not(.active) {
    background-color: #f5f5f5;
}

.section-header {
    margin-bottom: 25px;
}

.section-header h2 {
    font-size: 20px;
    margin-bottom: 5px;
}

.section-header p {
    color: #666;
    font-size: 14px;
}

/* Form Styling */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="tel"],
.form-group input[type="password"],
.form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
}

.form-group select {
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 15px center;
    background-size: 15px;
}

/* Toggle Switch Styling */
.toggle-group {
    margin-bottom: 20px;
}

.toggle-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #5e259b;
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.setting-description {
    font-size: 13px;
    color: #666;
    margin-top: 5px;
}

/* Checkbox Styling */
.checkbox-group {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.checkbox-group input[type="checkbox"] {
    margin-right: 10px;
}

/* Settings Subheader */
.settings-subheader {
    font-size: 16px;
    margin: 25px 0 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
}

/* Save Button */
.save-button {
    background-color: #5e259b;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 12px 20px;
    font-size: 16px;
    cursor: pointer;
    margin-top: 10px;
}

.save-button:hover {
    background-color: #4a1d7a;
}

/* Settings Section Display */
.settings-section {
    display: none;
}

.settings-section.active {
    display: block;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .settings-container {
        flex-direction: column;
    }
    
    .settings-sidebar {
        width: 100%;
    }
    
    .settings-nav {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .settings-nav-item {
        flex: 1;
        min-width: 120px;
        text-align: center;
        justify-content: center;
    }
    
    .settings-nav-item i {
        margin-right: 5px;
    }
}

@media (max-width: 576px) {
    .settings-nav {
        flex-direction: column;
    }
    
    .settings-nav-item {
        width: 100%;
        justify-content: flex-start;
    }
    
    .settings-nav-item i {
        margin-right: 10px;
    }
    
    .toggle-label {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .toggle-switch {
        margin-top: 10px;
    }
}
