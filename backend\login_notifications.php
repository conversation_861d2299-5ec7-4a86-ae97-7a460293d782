<?php
/**
 * Login Notification System
 * 
 * This file handles login notifications and message count management.
 */

require_once 'config.php';

/**
 * Create login notification for user
 * 
 * @param int $userId User ID
 * @return bool Success status
 */
function createLoginNotification($userId) {
    try {
        $db = getDbConnection();
        
        // Check if login notifications are enabled for this user
        $stmt = $db->prepare("
            SELECT login_notifications 
            FROM user_settings 
            WHERE user_id = ?
        ");
        $stmt->execute([$userId]);
        $settings = $stmt->fetch();
        
        if (!$settings || !$settings['login_notifications']) {
            return false;
        }
        
        // Create notification in database
        $stmt = $db->prepare("
            INSERT INTO notifications 
            (user_id, title, message, notification_type, created_at, is_read) 
            VALUES (?, ?, ?, ?, NOW(), 0)
        ");
        
        $title = 'Login Notification';
        $message = 'You have successfully logged in at ' . date('Y-m-d H:i:s');
        $type = 'ACCOUNT';
        
        $success = $stmt->execute([$userId, $title, $message, $type]);
        
        if ($success) {
            // Log the activity
            logActivity($userId, 'LOGIN_NOTIFICATION', 'Login notification created');
        }
        
        return $success;
        
    } catch (PDOException $e) {
        error_log("Login notification error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get unread message count for user
 * 
 * @param int $userId User ID
 * @return int Unread message count
 */
function getUnreadMessageCount($userId) {
    try {
        $db = getDbConnection();
        
        $stmt = $db->prepare("
            SELECT COUNT(*) as count 
            FROM notifications 
            WHERE user_id = ? AND is_read = 0 AND notification_type = 'ACCOUNT'
        ");
        $stmt->execute([$userId]);
        $result = $stmt->fetch();
        
        return (int)$result['count'];
        
    } catch (PDOException $e) {
        error_log("Get message count error: " . $e->getMessage());
        return 0;
    }
}

/**
 * Get login notifications for user
 * 
 * @param int $userId User ID
 * @param int $limit Number of notifications to retrieve
 * @return array Array of notifications
 */
function getLoginNotifications($userId, $limit = 10) {
    try {
        $db = getDbConnection();
        
        $stmt = $db->prepare("
            SELECT notification_id, title, message, created_at, is_read
            FROM notifications 
            WHERE user_id = ? AND notification_type = 'ACCOUNT'
            ORDER BY created_at DESC 
            LIMIT ?
        ");
        $stmt->execute([$userId, $limit]);
        
        return $stmt->fetchAll();
        
    } catch (PDOException $e) {
        error_log("Get login notifications error: " . $e->getMessage());
        return [];
    }
}

/**
 * Mark notification as read
 * 
 * @param int $notificationId Notification ID
 * @param int $userId User ID (for security)
 * @return bool Success status
 */
function markNotificationAsRead($notificationId, $userId) {
    try {
        $db = getDbConnection();
        
        $stmt = $db->prepare("
            UPDATE notifications 
            SET is_read = 1 
            WHERE notification_id = ? AND user_id = ?
        ");
        
        return $stmt->execute([$notificationId, $userId]);
        
    } catch (PDOException $e) {
        error_log("Mark notification read error: " . $e->getMessage());
        return false;
    }
}

/**
 * Mark all notifications as read for user
 * 
 * @param int $userId User ID
 * @return bool Success status
 */
function markAllNotificationsAsRead($userId) {
    try {
        $db = getDbConnection();
        
        $stmt = $db->prepare("
            UPDATE notifications 
            SET is_read = 1 
            WHERE user_id = ? AND notification_type = 'ACCOUNT'
        ");
        
        return $stmt->execute([$userId]);
        
    } catch (PDOException $e) {
        error_log("Mark all notifications read error: " . $e->getMessage());
        return false;
    }
}

/**
 * Clean up old notifications (older than 30 days)
 * 
 * @param int $userId User ID
 * @return bool Success status
 */
function cleanupOldNotifications($userId) {
    try {
        $db = getDbConnection();
        
        $stmt = $db->prepare("
            DELETE FROM notifications 
            WHERE user_id = ? AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)
        ");
        
        return $stmt->execute([$userId]);
        
    } catch (PDOException $e) {
        error_log("Cleanup notifications error: " . $e->getMessage());
        return false;
    }
}
?>
