<?php
/**
 * API Handler
 * 
 * This file handles API requests for the Student ID Application.
 */

require_once 'config.php';
require_once 'auth.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if the request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// Get the action from the request
$action = $_POST['action'] ?? '';

// Handle the action
switch ($action) {
    case 'register':
        handleRegister();
        break;
        
    case 'login':
        handleLogin();
        break;
        
    case 'logout':
        handleLogout();
        break;
        
    case 'apply_id_card':
        handleIdCardApplication();
        break;
        
    case 'upload_document':
        handleDocumentUpload();
        break;
        
    case 'update_profile':
        handleProfileUpdate();
        break;
        
    case 'update_settings':
        handleSettingsUpdate();
        break;
        
    case 'get_dashboard_data':
        handleGetDashboardData();
        break;
        
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        break;
}

/**
 * Handle user registration
 */
function handleRegister() {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    $email = $_POST['email'] ?? '';
    
    // Validate passwords match
    if ($password !== $confirmPassword) {
        echo json_encode(['success' => false, 'message' => 'Passwords do not match']);
        return;
    }
    
    // Register the user
    $result = registerUser($username, $password, $email);
    
    echo json_encode($result);
}

/**
 * Handle user login
 */
function handleLogin() {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    // Login the user
    $result = loginUser($username, $password);
    
    echo json_encode($result);
}

/**
 * Handle user logout
 */
function handleLogout() {
    // Check if user is logged in
    if (!isLoggedIn()) {
        echo json_encode(['success' => false, 'message' => 'Not logged in']);
        return;
    }
    
    // Logout the user
    logoutUser();
    
    echo json_encode(['success' => true, 'message' => 'Logout successful']);
}

/**
 * Handle ID card application
 */
function handleIdCardApplication() {
    // Check if user is logged in
    if (!isLoggedIn()) {
        echo json_encode(['success' => false, 'message' => 'Not logged in']);
        return;
    }
    
    // Get application data
    $userId = $_SESSION['user_id'];
    $fullName = $_POST['full_name'] ?? '';
    $studentId = $_POST['student_id'] ?? '';
    $department = $_POST['department'] ?? '';
    $program = $_POST['program'] ?? '';
    $enrollmentYear = $_POST['enrollment_year'] ?? '';
    $expectedGraduation = $_POST['expected_graduation'] ?? '';
    $emergencyContact = $_POST['emergency_contact'] ?? '';
    
    // Validate required fields
    if (empty($fullName) || empty($studentId) || empty($department)) {
        echo json_encode(['success' => false, 'message' => 'Required fields are missing']);
        return;
    }
    
    try {
        $db = getDbConnection();
        
        // Update user profile
        $stmt = $db->prepare("
            UPDATE user_profiles 
            SET full_name = ?, student_id = ?, department = ?, program = ?, 
                enrollment_year = ?, expected_graduation = ?, emergency_contact = ?
            WHERE user_id = ?
        ");
        $stmt->execute([
            $fullName, $studentId, $department, $program, 
            $enrollmentYear, $expectedGraduation, $emergencyContact, $userId
        ]);
        
        // Check if user already has a pending application
        $stmt = $db->prepare("
            SELECT application_id FROM id_applications 
            WHERE user_id = ? AND status IN ('PENDING', 'APPROVED')
        ");
        $stmt->execute([$userId]);
        
        if ($stmt->rowCount() > 0) {
            echo json_encode(['success' => false, 'message' => 'You already have a pending or approved application']);
            return;
        }
        
        // Create new ID card application
        $stmt = $db->prepare("
            INSERT INTO id_applications (user_id, application_type, status) 
            VALUES (?, 'NEW', 'PENDING')
        ");
        $stmt->execute([$userId]);
        
        $applicationId = $db->lastInsertId();
        
        // Create notification for the user
        $stmt = $db->prepare("
            INSERT INTO notifications (user_id, title, message, notification_type) 
            VALUES (?, 'ID Card Application Submitted', 'Your ID card application has been submitted and is pending review.', 'ID_CARD')
        ");
        $stmt->execute([$userId]);
        
        // Log the application
        logActivity($userId, 'ID_APPLICATION', 'User submitted ID card application');
        
        echo json_encode([
            'success' => true, 
            'message' => 'ID card application submitted successfully',
            'application_id' => $applicationId
        ]);
    } catch (PDOException $e) {
        error_log("ID Card Application Error: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Application failed. Please try again later.']);
    }
}

/**
 * Handle document upload
 */
function handleDocumentUpload() {
    // Check if user is logged in
    if (!isLoggedIn()) {
        echo json_encode(['success' => false, 'message' => 'Not logged in']);
        return;
    }
    
    // Check if file was uploaded
    if (!isset($_FILES['document']) || $_FILES['document']['error'] !== UPLOAD_ERR_OK) {
        echo json_encode(['success' => false, 'message' => 'No file uploaded or upload error']);
        return;
    }
    
    $userId = $_SESSION['user_id'];
    $documentType = $_POST['document_type'] ?? '';
    $file = $_FILES['document'];
    
    // Validate document type
    $allowedTypes = ['GOVERNMENT_ID', 'ENROLLMENT_VERIFICATION', 'TUITION_RECEIPT', 'MEDICAL_INSURANCE', 'HOUSING_CONTRACT', 'OTHER'];
    if (!in_array($documentType, $allowedTypes)) {
        echo json_encode(['success' => false, 'message' => 'Invalid document type']);
        return;
    }
    
    // Validate file size
    if ($file['size'] > MAX_FILE_SIZE) {
        echo json_encode(['success' => false, 'message' => 'File size exceeds the limit of 2MB']);
        return;
    }
    
    // Validate file extension
    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($fileExtension, ALLOWED_EXTENSIONS)) {
        echo json_encode(['success' => false, 'message' => 'Invalid file type. Allowed types: ' . implode(', ', ALLOWED_EXTENSIONS)]);
        return;
    }
    
    // Generate unique filename
    $newFilename = uniqid('doc_') . '.' . $fileExtension;
    $uploadPath = UPLOAD_DIR . $newFilename;
    
    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $uploadPath)) {
        echo json_encode(['success' => false, 'message' => 'Failed to upload file']);
        return;
    }
    
    try {
        $db = getDbConnection();
        
        // Save document information to database
        $stmt = $db->prepare("
            INSERT INTO documents (user_id, document_type, file_name, file_path, file_size, mime_type) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $userId, 
            $documentType, 
            $file['name'], 
            $newFilename, 
            $file['size'], 
            $file['type']
        ]);
        
        $documentId = $db->lastInsertId();
        
        // Log the document upload
        logActivity($userId, 'DOCUMENT_UPLOAD', 'User uploaded document: ' . $documentType);
        
        echo json_encode([
            'success' => true, 
            'message' => 'Document uploaded successfully',
            'document_id' => $documentId
        ]);
    } catch (PDOException $e) {
        error_log("Document Upload Error: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Upload failed. Please try again later.']);
    }
}

/**
 * Handle profile update
 */
function handleProfileUpdate() {
    // Check if user is logged in
    if (!isLoggedIn()) {
        echo json_encode(['success' => false, 'message' => 'Not logged in']);
        return;
    }
    
    $userId = $_SESSION['user_id'];
    $fullName = $_POST['full_name'] ?? '';
    
    // Validate required fields
    if (empty($fullName)) {
        echo json_encode(['success' => false, 'message' => 'Full name is required']);
        return;
    }
    
    try {
        $db = getDbConnection();
        
        // Update user profile
        $stmt = $db->prepare("UPDATE user_profiles SET full_name = ? WHERE user_id = ?");
        $stmt->execute([$fullName, $userId]);
        
        // Update session variable
        $_SESSION['full_name'] = $fullName;
        
        // Log the profile update
        logActivity($userId, 'PROFILE_UPDATE', 'User updated profile information');
        
        echo json_encode(['success' => true, 'message' => 'Profile updated successfully']);
    } catch (PDOException $e) {
        error_log("Profile Update Error: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Update failed. Please try again later.']);
    }
}

/**
 * Handle settings update
 */
function handleSettingsUpdate() {
    // Check if user is logged in
    if (!isLoggedIn()) {
        echo json_encode(['success' => false, 'message' => 'Not logged in']);
        return;
    }
    
    $userId = $_SESSION['user_id'];
    
    // Get settings data
    $theme = $_POST['theme'] ?? 'light';
    $fontSize = $_POST['font_size'] ?? 100;
    $reduceAnimations = isset($_POST['reduce_animations']) ? 1 : 0;
    $highContrast = isset($_POST['high_contrast']) ? 1 : 0;
    $fontFamily = $_POST['font_family'] ?? 'default';
    $lineSpacing = $_POST['line_spacing'] ?? 150;
    
    try {
        $db = getDbConnection();
        
        // Update user settings
        $stmt = $db->prepare("
            UPDATE user_settings 
            SET theme = ?, font_size = ?, reduce_animations = ?, high_contrast = ?, 
                font_family = ?, line_spacing = ?
            WHERE user_id = ?
        ");
        $stmt->execute([
            $theme, $fontSize, $reduceAnimations, $highContrast, 
            $fontFamily, $lineSpacing, $userId
        ]);
        
        // Log the settings update
        logActivity($userId, 'SETTINGS_UPDATE', 'User updated display settings');
        
        echo json_encode(['success' => true, 'message' => 'Settings updated successfully']);
    } catch (PDOException $e) {
        error_log("Settings Update Error: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Update failed. Please try again later.']);
    }
}

/**
 * Handle get dashboard data
 */
function handleGetDashboardData() {
    // Check if user is logged in
    if (!isLoggedIn()) {
        echo json_encode(['success' => false, 'message' => 'Not logged in']);
        return;
    }
    
    $userId = $_SESSION['user_id'];
    
    try {
        $db = getDbConnection();
        
        // Get dashboard data from view
        $stmt = $db->prepare("SELECT * FROM dashboard_view WHERE user_id = ?");
        $stmt->execute([$userId]);
        $dashboardData = $stmt->fetch();
        
        // Get recent notifications
        $stmt = $db->prepare("
            SELECT notification_id, title, message, notification_type, created_at, is_read
            FROM notifications
            WHERE user_id = ?
            ORDER BY created_at DESC
            LIMIT 5
        ");
        $stmt->execute([$userId]);
        $notifications = $stmt->fetchAll();
        
        echo json_encode([
            'success' => true,
            'dashboard' => $dashboardData,
            'notifications' => $notifications
        ]);
    } catch (PDOException $e) {
        error_log("Get Dashboard Data Error: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Failed to get dashboard data. Please try again later.']);
    }
}
