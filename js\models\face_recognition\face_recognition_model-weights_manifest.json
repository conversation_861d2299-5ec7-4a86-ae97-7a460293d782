{"modelTopology": {"class_name": "Model", "config": {"name": "face_recognition", "layers": [{"name": "input_1", "class_name": "InputLayer", "config": {"batch_input_shape": [null, 150, 150, 3], "dtype": "float32", "sparse": false, "name": "input_1"}}]}}, "weightsManifest": [{"paths": ["face_recognition_model-shard1.bin", "face_recognition_model-shard2.bin"], "weights": [{"name": "conv2d_1/kernel", "shape": [3, 3, 3, 32], "dtype": "float32"}, {"name": "conv2d_1/bias", "shape": [32], "dtype": "float32"}, {"name": "dense_1/kernel", "shape": [8192, 128], "dtype": "float32"}, {"name": "dense_1/bias", "shape": [128], "dtype": "float32"}]}], "format": "layers-model", "generatedBy": "TensorFlow.js tfjs-layers v1.0.0", "convertedBy": "TensorFlow.js Converter v1.0.0"}