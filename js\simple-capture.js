/**
 * Simplified Two-Step Camera Capture System
 *
 * This module handles:
 * 1. ID Document capture with front-facing camera
 * 2. Face capture with front-facing camera
 * 3. Basic photo storage in base64 format
 *
 * No advanced facial recognition - just basic capture functionality
 */

class SimpleCaptureSystem {
    constructor() {
        this.currentStream = null;
        this.activeCapture = null; // 'id' or 'face'

        // DOM elements for both New User and Existing User forms
        this.elements = {
            // New User ID Capture elements
            captureIdBtn: document.getElementById('captureIdBtn'),
            idCameraInterface: document.getElementById('idCameraInterface'),
            idCameraVideo: document.getElementById('idCameraVideo'),
            idCameraCanvas: document.getElementById('idCameraCanvas'),
            takeIdPhotoBtn: document.getElementById('takeIdPhotoBtn'),
            cancelIdCameraBtn: document.getElementById('cancelIdCameraBtn'),
            idPhotoPreview: document.getElementById('idPhotoPreview'),
            idPreviewImage: document.getElementById('idPreviewImage'),
            retakeIdBtn: document.getElementById('retakeIdBtn'),
            idPhotoData: document.getElementById('idPhotoData'),

            // New User Face Capture elements
            captureFaceBtn: document.getElementById('captureFaceBtn'),
            faceCameraInterface: document.getElementById('faceCameraInterface'),
            faceCameraVideo: document.getElementById('faceCameraVideo'),
            faceCameraCanvas: document.getElementById('faceCameraCanvas'),
            takeFacePhotoBtn: document.getElementById('takeFacePhotoBtn'),
            cancelFaceCameraBtn: document.getElementById('cancelFaceCameraBtn'),
            facePhotoPreview: document.getElementById('facePhotoPreview'),
            facePreviewImage: document.getElementById('facePreviewImage'),
            retakeFaceBtn: document.getElementById('retakeFaceBtn'),
            facePhotoData: document.getElementById('facePhotoData'),

            // Existing User ID Capture elements
            existingCaptureIdBtn: document.getElementById('existingCaptureIdBtn'),
            existingIdCameraInterface: document.getElementById('existingIdCameraInterface'),
            existingIdCameraVideo: document.getElementById('existingIdCameraVideo'),
            existingIdCameraCanvas: document.getElementById('existingIdCameraCanvas'),
            existingTakeIdPhotoBtn: document.getElementById('existingTakeIdPhotoBtn'),
            existingCancelIdCameraBtn: document.getElementById('existingCancelIdCameraBtn'),
            existingIdPhotoPreview: document.getElementById('existingIdPhotoPreview'),
            existingIdPreviewImage: document.getElementById('existingIdPreviewImage'),
            existingRetakeIdBtn: document.getElementById('existingRetakeIdBtn'),
            existingIdPhotoData: document.getElementById('existingIdPhotoData'),

            // Existing User Face Capture elements
            existingCaptureFaceBtn: document.getElementById('existingCaptureFaceBtn'),
            existingFaceCameraInterface: document.getElementById('existingFaceCameraInterface'),
            existingFaceCameraVideo: document.getElementById('existingFaceCameraVideo'),
            existingFaceCameraCanvas: document.getElementById('existingFaceCameraCanvas'),
            existingTakeFacePhotoBtn: document.getElementById('existingTakeFacePhotoBtn'),
            existingCancelFaceCameraBtn: document.getElementById('existingCancelFaceCameraBtn'),
            existingFacePhotoPreview: document.getElementById('existingFacePhotoPreview'),
            existingFacePreviewImage: document.getElementById('existingFacePreviewImage'),
            existingRetakeFaceBtn: document.getElementById('existingRetakeFaceBtn'),
            existingFacePhotoData: document.getElementById('existingFacePhotoData'),

            // ID Card Preview
            idCardPreviewPhoto: document.getElementById('idCardPreviewPhoto'),

            // Forms
            idCardForm: document.getElementById('idCardForm'),
            existingUserIdCardForm: document.getElementById('existingUserIdCardForm')
        };

        this.init();
    }

    init() {
        console.log('Initializing Simple Capture System...');
        console.log('Available elements:', {
            captureIdBtn: !!this.elements.captureIdBtn,
            captureFaceBtn: !!this.elements.captureFaceBtn,
            idCameraInterface: !!this.elements.idCameraInterface,
            faceCameraInterface: !!this.elements.faceCameraInterface
        });
        this.setupEventListeners();
        console.log('Simple Capture System initialized successfully');
    }

    setupEventListeners() {
        // New User ID Capture event listeners
        this.elements.captureIdBtn?.addEventListener('click', () => {
            console.log('New User ID Capture button clicked');
            this.startIdCapture('new');
        });

        this.elements.takeIdPhotoBtn?.addEventListener('click', () => {
            console.log('New User Take ID Photo button clicked');
            this.captureIdPhoto('new');
        });

        this.elements.cancelIdCameraBtn?.addEventListener('click', () => {
            console.log('New User Cancel ID Camera button clicked');
            this.cancelCapture('new');
        });

        this.elements.retakeIdBtn?.addEventListener('click', () => {
            console.log('New User Retake ID button clicked');
            this.retakeIdPhoto('new');
        });

        // New User Face Capture event listeners
        this.elements.captureFaceBtn?.addEventListener('click', () => {
            console.log('New User Face Capture button clicked');
            this.startFaceCapture('new');
        });

        this.elements.takeFacePhotoBtn?.addEventListener('click', () => {
            console.log('New User Take Face Photo button clicked');
            this.captureFacePhoto('new');
        });

        this.elements.cancelFaceCameraBtn?.addEventListener('click', () => {
            console.log('New User Cancel Face Camera button clicked');
            this.cancelCapture('new');
        });

        this.elements.retakeFaceBtn?.addEventListener('click', () => {
            console.log('New User Retake Face button clicked');
            this.retakeFacePhoto('new');
        });

        // Existing User ID Capture event listeners
        this.elements.existingCaptureIdBtn?.addEventListener('click', () => {
            console.log('Existing User ID Capture button clicked');
            this.startIdCapture('existing');
        });

        this.elements.existingTakeIdPhotoBtn?.addEventListener('click', () => {
            console.log('Existing User Take ID Photo button clicked');
            this.captureIdPhoto('existing');
        });

        this.elements.existingCancelIdCameraBtn?.addEventListener('click', () => {
            console.log('Existing User Cancel ID Camera button clicked');
            this.cancelCapture('existing');
        });

        this.elements.existingRetakeIdBtn?.addEventListener('click', () => {
            console.log('Existing User Retake ID button clicked');
            this.retakeIdPhoto('existing');
        });

        // Existing User Face Capture event listeners
        this.elements.existingCaptureFaceBtn?.addEventListener('click', () => {
            console.log('Existing User Face Capture button clicked');
            this.startFaceCapture('existing');
        });

        this.elements.existingTakeFacePhotoBtn?.addEventListener('click', () => {
            console.log('Existing User Take Face Photo button clicked');
            this.captureFacePhoto('existing');
        });

        this.elements.existingCancelFaceCameraBtn?.addEventListener('click', () => {
            console.log('Existing User Cancel Face Camera button clicked');
            this.cancelCapture('existing');
        });

        this.elements.existingRetakeFaceBtn?.addEventListener('click', () => {
            console.log('Existing User Retake Face button clicked');
            this.retakeFacePhoto('existing');
        });

        // Form submission validation
        this.elements.idCardForm?.addEventListener('submit', (e) => {
            this.validateFormSubmission(e, 'new');
        });

        this.elements.existingUserIdCardForm?.addEventListener('submit', (e) => {
            this.validateFormSubmission(e, 'existing');
        });
    }

    async startIdCapture(userType = 'new') {
        console.log(`Starting ID capture for ${userType} user...`);
        this.activeCapture = 'id';
        this.activeUserType = userType;

        try {
            await this.requestCamera(userType);
            this.showCameraInterface('id', userType);
        } catch (error) {
            console.error('Failed to start ID capture:', error);
            this.showError('Camera access denied. Please allow camera permissions and try again.');
        }
    }

    async startFaceCapture(userType = 'new') {
        console.log(`Starting face capture for ${userType} user...`);
        this.activeCapture = 'face';
        this.activeUserType = userType;

        try {
            await this.requestCamera(userType);
            this.showCameraInterface('face', userType);
        } catch (error) {
            console.error('Failed to start face capture:', error);
            this.showError('Camera access denied. Please allow camera permissions and try again.');
        }
    }

    async requestCamera(userType = 'new') {
        console.log(`Requesting camera access for ${userType} user...`);

        // Standardized camera constraints - front-facing camera for both captures
        const constraints = {
            video: {
                width: { ideal: 1280 },
                height: { ideal: 720 },
                facingMode: 'user' // Front-facing camera for both ID and face capture
            }
        };

        try {
            this.currentStream = await navigator.mediaDevices.getUserMedia(constraints);
            console.log('Camera access granted successfully');

            // Set video source based on active capture and user type
            if (this.activeCapture === 'id') {
                if (userType === 'new') {
                    this.elements.idCameraVideo.srcObject = this.currentStream;
                } else {
                    this.elements.existingIdCameraVideo.srcObject = this.currentStream;
                }
            } else if (this.activeCapture === 'face') {
                if (userType === 'new') {
                    this.elements.faceCameraVideo.srcObject = this.currentStream;
                } else {
                    this.elements.existingFaceCameraVideo.srcObject = this.currentStream;
                }
            }

        } catch (error) {
            console.error('Camera access error:', error);
            console.error('Error name:', error.name);
            console.error('Error message:', error.message);

            let errorMessage = 'Camera access denied. ';
            if (error.name === 'NotAllowedError') {
                errorMessage += 'Please allow camera permissions and try again.';
            } else if (error.name === 'NotFoundError') {
                errorMessage += 'No camera found on this device.';
            } else if (error.name === 'NotSupportedError') {
                errorMessage += 'Camera not supported in this browser.';
            } else {
                errorMessage += 'Please check your camera settings and try again.';
            }

            throw new Error(errorMessage);
        }
    }

    showCameraInterface(type) {
        console.log(`Showing camera interface for ${type} capture`);

        if (type === 'id') {
            this.elements.idCameraInterface.style.display = 'block';
        } else if (type === 'face') {
            this.elements.faceCameraInterface.style.display = 'block';
        }
    }

    captureIdPhoto() {
        console.log('Capturing ID photo...');

        try {
            const canvas = this.elements.idCameraCanvas;
            const video = this.elements.idCameraVideo;

            // Set canvas dimensions to match video
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;

            // Draw video frame to canvas
            const ctx = canvas.getContext('2d');
            ctx.drawImage(video, 0, 0);

            // Convert to base64
            const photoDataUrl = canvas.toDataURL('image/jpeg', 0.8);

            // Store photo data
            this.elements.idPhotoData.value = photoDataUrl;

            // Update UI
            this.displayIdPhoto(photoDataUrl);
            this.stopCamera();
            this.hideIdCameraInterface();
            this.showIdPhotoPreview();

            console.log('ID photo captured successfully');

        } catch (error) {
            console.error('Error capturing ID photo:', error);
            this.showError('Failed to capture photo. Please try again.');
        }
    }

    captureFacePhoto() {
        console.log('Capturing face photo...');

        try {
            const canvas = this.elements.faceCameraCanvas;
            const video = this.elements.faceCameraVideo;

            // Set canvas dimensions to match video
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;

            // Draw video frame to canvas
            const ctx = canvas.getContext('2d');
            ctx.drawImage(video, 0, 0);

            // Convert to base64
            const photoDataUrl = canvas.toDataURL('image/jpeg', 0.8);

            // Store photo data
            this.elements.facePhotoData.value = photoDataUrl;

            // Update UI
            this.displayFacePhoto(photoDataUrl);
            this.stopCamera();
            this.hideFaceCameraInterface();
            this.showFacePhotoPreview();

            console.log('Face photo captured successfully');

        } catch (error) {
            console.error('Error capturing face photo:', error);
            this.showError('Failed to capture photo. Please try again.');
        }
    }

    displayIdPhoto(photoDataUrl) {
        // Update ID preview
        this.elements.idPreviewImage.innerHTML = `<img src="${photoDataUrl}" alt="ID Document">`;

        // Update ID card preview
        this.elements.idCardPreviewPhoto.innerHTML = `<img src="${photoDataUrl}" alt="ID Photo" style="width: 100%; height: 100%; object-fit: cover;">`;
    }

    displayFacePhoto(photoDataUrl) {
        // Update face preview
        this.elements.facePreviewImage.innerHTML = `<img src="${photoDataUrl}" alt="Face Photo">`;
    }

    stopCamera() {
        if (this.currentStream) {
            this.currentStream.getTracks().forEach(track => track.stop());
            this.currentStream = null;
            console.log('Camera stopped');
        }
    }

    cancelCapture() {
        console.log('Canceling capture...');
        this.stopCamera();
        this.hideIdCameraInterface();
        this.hideFaceCameraInterface();
        this.activeCapture = null;
    }

    hideIdCameraInterface() {
        this.elements.idCameraInterface.style.display = 'none';
    }

    hideFaceCameraInterface() {
        this.elements.faceCameraInterface.style.display = 'none';
    }

    showIdPhotoPreview() {
        this.elements.idPhotoPreview.style.display = 'block';
    }

    showFacePhotoPreview() {
        this.elements.facePhotoPreview.style.display = 'block';
    }

    retakeIdPhoto() {
        console.log('Retaking ID photo...');
        this.elements.idPhotoData.value = '';
        this.elements.idPhotoPreview.style.display = 'none';
        this.elements.idCardPreviewPhoto.innerHTML = '<i class="fas fa-user photo-icon"></i><p class="photo-placeholder">Photo</p>';
        this.startIdCapture();
    }

    retakeFacePhoto() {
        console.log('Retaking face photo...');
        this.elements.facePhotoData.value = '';
        this.elements.facePhotoPreview.style.display = 'none';
        this.startFaceCapture();
    }

    validateFormSubmission(event) {
        console.log('Validating form submission...');

        const idPhotoData = this.elements.idPhotoData.value;
        const facePhotoData = this.elements.facePhotoData.value;

        if (!idPhotoData) {
            event.preventDefault();
            this.showError('Please capture your ID document first.');
            return false;
        }

        if (!facePhotoData) {
            event.preventDefault();
            this.showError('Please capture your face photo first.');
            return false;
        }

        console.log('Form validation passed - both photos captured');
        return true;
    }

    showError(message) {
        console.error('Error:', message);
        alert(message); // Simple error display
    }

    showSuccess(message) {
        console.log('Success:', message);
        alert(message); // Simple success display
    }
}

// Initialize the simple capture system when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, checking for capture elements...');

    // Only initialize if we're on the create ID page
    if (document.getElementById('captureIdBtn')) {
        console.log('Capture elements found, initializing Simple Capture System...');
        window.simpleCaptureSystem = new SimpleCaptureSystem();
    } else {
        console.log('Capture elements not found, skipping initialization');
    }
});
