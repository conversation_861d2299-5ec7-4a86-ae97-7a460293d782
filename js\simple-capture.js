/**
 * Simplified Two-Step Camera Capture System
 *
 * This module handles:
 * 1. ID Document capture with front-facing camera
 * 2. Face capture with front-facing camera
 * 3. Basic photo storage in base64 format
 *
 * No advanced facial recognition - just basic capture functionality
 */

class SimpleCaptureSystem {
    constructor() {
        this.currentStream = null;
        this.activeCapture = null; // 'id' or 'face'
        this.activeUserType = null; // 'new' or 'existing'

        // Track completion status for sequential enforcement
        this.captureStatus = {
            new: { idCaptured: false, faceCaptured: false },
            existing: { idCaptured: false, faceCaptured: false }
        };

        // DOM elements for both New User and Existing User forms
        this.elements = {
            // New User ID Capture elements
            captureIdBtn: document.getElementById('captureIdBtn'),
            idCameraInterface: document.getElementById('idCameraInterface'),
            idCameraVideo: document.getElementById('idCameraVideo'),
            idCameraCanvas: document.getElementById('idCameraCanvas'),
            takeIdPhotoBtn: document.getElementById('takeIdPhotoBtn'),
            cancelIdCameraBtn: document.getElementById('cancelIdCameraBtn'),
            idPhotoPreview: document.getElementById('idPhotoPreview'),
            idPreviewImage: document.getElementById('idPreviewImage'),
            retakeIdBtn: document.getElementById('retakeIdBtn'),
            idPhotoData: document.getElementById('idPhotoData'),

            // New User Face Capture elements
            captureFaceBtn: document.getElementById('captureFaceBtn'),
            faceCameraInterface: document.getElementById('faceCameraInterface'),
            faceCameraVideo: document.getElementById('faceCameraVideo'),
            faceCameraCanvas: document.getElementById('faceCameraCanvas'),
            takeFacePhotoBtn: document.getElementById('takeFacePhotoBtn'),
            cancelFaceCameraBtn: document.getElementById('cancelFaceCameraBtn'),
            facePhotoPreview: document.getElementById('facePhotoPreview'),
            facePreviewImage: document.getElementById('facePreviewImage'),
            retakeFaceBtn: document.getElementById('retakeFaceBtn'),
            facePhotoData: document.getElementById('facePhotoData'),

            // Existing User ID Capture elements
            existingCaptureIdBtn: document.getElementById('existingCaptureIdBtn'),
            existingIdCameraInterface: document.getElementById('existingIdCameraInterface'),
            existingIdCameraVideo: document.getElementById('existingIdCameraVideo'),
            existingIdCameraCanvas: document.getElementById('existingIdCameraCanvas'),
            existingTakeIdPhotoBtn: document.getElementById('existingTakeIdPhotoBtn'),
            existingCancelIdCameraBtn: document.getElementById('existingCancelIdCameraBtn'),
            existingIdPhotoPreview: document.getElementById('existingIdPhotoPreview'),
            existingIdPreviewImage: document.getElementById('existingIdPreviewImage'),
            existingRetakeIdBtn: document.getElementById('existingRetakeIdBtn'),
            existingIdPhotoData: document.getElementById('existingIdPhotoData'),

            // Existing User Face Capture elements
            existingCaptureFaceBtn: document.getElementById('existingCaptureFaceBtn'),
            existingFaceCameraInterface: document.getElementById('existingFaceCameraInterface'),
            existingFaceCameraVideo: document.getElementById('existingFaceCameraVideo'),
            existingFaceCameraCanvas: document.getElementById('existingFaceCameraCanvas'),
            existingTakeFacePhotoBtn: document.getElementById('existingTakeFacePhotoBtn'),
            existingCancelFaceCameraBtn: document.getElementById('existingCancelFaceCameraBtn'),
            existingFacePhotoPreview: document.getElementById('existingFacePhotoPreview'),
            existingFacePreviewImage: document.getElementById('existingFacePreviewImage'),
            existingRetakeFaceBtn: document.getElementById('existingRetakeFaceBtn'),
            existingFacePhotoData: document.getElementById('existingFacePhotoData'),

            // ID Card Preview
            idCardPreviewPhoto: document.getElementById('idCardPreviewPhoto'),

            // Forms
            idCardForm: document.getElementById('idCardForm'),
            existingUserIdCardForm: document.getElementById('existingUserIdCardForm')
        };

        this.init();
    }

    init() {
        console.log('Initializing Simple Capture System...');
        console.log('Available elements:', {
            captureIdBtn: !!this.elements.captureIdBtn,
            captureFaceBtn: !!this.elements.captureFaceBtn,
            idCameraInterface: !!this.elements.idCameraInterface,
            faceCameraInterface: !!this.elements.faceCameraInterface
        });
        this.setupEventListeners();

        // Initially disable face capture for both user types
        this.disableFaceCapture('new');
        this.disableFaceCapture('existing');

        // Initialize step indicators
        this.updateStepIndicators('new');
        this.updateStepIndicators('existing');

        console.log('Simple Capture System initialized successfully');
    }

    setupEventListeners() {
        // New User ID Capture event listeners
        this.elements.captureIdBtn?.addEventListener('click', () => {
            console.log('New User ID Capture button clicked');
            this.startIdCapture('new');
        });

        this.elements.takeIdPhotoBtn?.addEventListener('click', () => {
            console.log('New User Take ID Photo button clicked');
            this.captureIdPhoto('new');
        });

        this.elements.cancelIdCameraBtn?.addEventListener('click', () => {
            console.log('New User Cancel ID Camera button clicked');
            this.cancelCapture('new');
        });

        this.elements.retakeIdBtn?.addEventListener('click', () => {
            console.log('New User Retake ID button clicked');
            this.retakeIdPhoto('new');
        });

        // New User Face Capture event listeners
        this.elements.captureFaceBtn?.addEventListener('click', () => {
            console.log('New User Face Capture button clicked');
            this.startFaceCapture('new');
        });

        this.elements.takeFacePhotoBtn?.addEventListener('click', () => {
            console.log('New User Take Face Photo button clicked');
            this.captureFacePhoto('new');
        });

        this.elements.cancelFaceCameraBtn?.addEventListener('click', () => {
            console.log('New User Cancel Face Camera button clicked');
            this.cancelCapture('new');
        });

        this.elements.retakeFaceBtn?.addEventListener('click', () => {
            console.log('New User Retake Face button clicked');
            this.retakeFacePhoto('new');
        });

        // Existing User ID Capture event listeners
        this.elements.existingCaptureIdBtn?.addEventListener('click', () => {
            console.log('Existing User ID Capture button clicked');
            this.startIdCapture('existing');
        });

        this.elements.existingTakeIdPhotoBtn?.addEventListener('click', () => {
            console.log('Existing User Take ID Photo button clicked');
            this.captureIdPhoto('existing');
        });

        this.elements.existingCancelIdCameraBtn?.addEventListener('click', () => {
            console.log('Existing User Cancel ID Camera button clicked');
            this.cancelCapture('existing');
        });

        this.elements.existingRetakeIdBtn?.addEventListener('click', () => {
            console.log('Existing User Retake ID button clicked');
            this.retakeIdPhoto('existing');
        });

        // Existing User Face Capture event listeners
        this.elements.existingCaptureFaceBtn?.addEventListener('click', () => {
            console.log('Existing User Face Capture button clicked');
            this.startFaceCapture('existing');
        });

        this.elements.existingTakeFacePhotoBtn?.addEventListener('click', () => {
            console.log('Existing User Take Face Photo button clicked');
            this.captureFacePhoto('existing');
        });

        this.elements.existingCancelFaceCameraBtn?.addEventListener('click', () => {
            console.log('Existing User Cancel Face Camera button clicked');
            this.cancelCapture('existing');
        });

        this.elements.existingRetakeFaceBtn?.addEventListener('click', () => {
            console.log('Existing User Retake Face button clicked');
            this.retakeFacePhoto('existing');
        });

        // Form submission validation
        this.elements.idCardForm?.addEventListener('submit', (e) => {
            this.validateFormSubmission(e, 'new');
        });

        this.elements.existingUserIdCardForm?.addEventListener('submit', (e) => {
            this.validateFormSubmission(e, 'existing');
        });
    }

    async startIdCapture(userType = 'new') {
        console.log(`Starting ID capture for ${userType} user...`);
        this.activeCapture = 'id';
        this.activeUserType = userType;

        try {
            await this.requestCamera(userType);
            this.showCameraInterface('id', userType);
        } catch (error) {
            console.error('Failed to start ID capture:', error);
            this.showError('Camera access denied. Please allow camera permissions and try again.');
        }
    }

    async startFaceCapture(userType = 'new') {
        console.log(`Starting face capture for ${userType} user...`);

        // Enforce sequential step completion - ID must be captured first
        if (!this.captureStatus[userType].idCaptured) {
            this.showError('Please complete Step 1: Capture ID Document first before proceeding to face capture.');
            return;
        }

        this.activeCapture = 'face';
        this.activeUserType = userType;

        try {
            await this.requestCamera(userType);
            this.showCameraInterface('face', userType);
        } catch (error) {
            console.error('Failed to start face capture:', error);
            this.showError('Camera access denied. Please allow camera permissions and try again.');
        }
    }

    async requestCamera(userType = 'new') {
        console.log(`Requesting camera access for ${userType} user...`);

        // Standardized camera constraints - front-facing camera for both captures
        const constraints = {
            video: {
                width: { ideal: 1280 },
                height: { ideal: 720 },
                facingMode: 'user' // Front-facing camera for both ID and face capture
            }
        };

        try {
            this.currentStream = await navigator.mediaDevices.getUserMedia(constraints);
            console.log('Camera access granted successfully');

            // Set video source based on active capture and user type
            if (this.activeCapture === 'id') {
                if (userType === 'new') {
                    this.elements.idCameraVideo.srcObject = this.currentStream;
                } else {
                    this.elements.existingIdCameraVideo.srcObject = this.currentStream;
                }
            } else if (this.activeCapture === 'face') {
                if (userType === 'new') {
                    this.elements.faceCameraVideo.srcObject = this.currentStream;
                } else {
                    this.elements.existingFaceCameraVideo.srcObject = this.currentStream;
                }
            }

        } catch (error) {
            console.error('Camera access error:', error);
            console.error('Error name:', error.name);
            console.error('Error message:', error.message);

            let errorMessage = 'Camera access denied. ';
            if (error.name === 'NotAllowedError') {
                errorMessage += 'Please allow camera permissions and try again.';
            } else if (error.name === 'NotFoundError') {
                errorMessage += 'No camera found on this device.';
            } else if (error.name === 'NotSupportedError') {
                errorMessage += 'Camera not supported in this browser.';
            } else {
                errorMessage += 'Please check your camera settings and try again.';
            }

            throw new Error(errorMessage);
        }
    }

    showCameraInterface(type, userType = 'new') {
        console.log(`Showing camera interface for ${type} capture (${userType} user)`);

        if (type === 'id') {
            if (userType === 'new') {
                this.elements.idCameraInterface.style.display = 'block';
            } else {
                this.elements.existingIdCameraInterface.style.display = 'block';
            }
        } else if (type === 'face') {
            if (userType === 'new') {
                this.elements.faceCameraInterface.style.display = 'block';
            } else {
                this.elements.existingFaceCameraInterface.style.display = 'block';
            }
        }
    }

    captureIdPhoto(userType = 'new') {
        console.log(`Capturing ID photo for ${userType} user...`);

        try {
            const canvas = userType === 'new' ? this.elements.idCameraCanvas : this.elements.existingIdCameraCanvas;
            const video = userType === 'new' ? this.elements.idCameraVideo : this.elements.existingIdCameraVideo;
            const photoDataInput = userType === 'new' ? this.elements.idPhotoData : this.elements.existingIdPhotoData;

            // Set canvas dimensions to match video
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;

            // Draw video frame to canvas
            const ctx = canvas.getContext('2d');
            ctx.drawImage(video, 0, 0);

            // Convert to base64
            const photoDataUrl = canvas.toDataURL('image/jpeg', 0.8);

            // Store photo data
            photoDataInput.value = photoDataUrl;

            // Update capture status
            this.captureStatus[userType].idCaptured = true;

            // Update UI
            this.displayIdPhoto(photoDataUrl, userType);
            this.stopCamera();
            this.hideIdCameraInterface(userType);
            this.showIdPhotoPreview(userType);

            // Enable face capture step
            this.enableFaceCapture(userType);

            // Update step indicators
            this.updateStepIndicators(userType);

            // Dispatch event for form validation update
            document.dispatchEvent(new CustomEvent('photoCaptured', {
                detail: { type: 'id', userType: userType }
            }));

            console.log(`ID photo captured successfully for ${userType} user`);

        } catch (error) {
            console.error('Error capturing ID photo:', error);
            this.showError('Failed to capture photo. Please try again.');
        }
    }

    captureFacePhoto(userType = 'new') {
        console.log(`Capturing face photo for ${userType} user...`);

        try {
            const canvas = userType === 'new' ? this.elements.faceCameraCanvas : this.elements.existingFaceCameraCanvas;
            const video = userType === 'new' ? this.elements.faceCameraVideo : this.elements.existingFaceCameraVideo;
            const photoDataInput = userType === 'new' ? this.elements.facePhotoData : this.elements.existingFacePhotoData;

            // Set canvas dimensions to match video
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;

            // Draw video frame to canvas
            const ctx = canvas.getContext('2d');
            ctx.drawImage(video, 0, 0);

            // Convert to base64
            const photoDataUrl = canvas.toDataURL('image/jpeg', 0.8);

            // Store photo data
            photoDataInput.value = photoDataUrl;

            // Update capture status
            this.captureStatus[userType].faceCaptured = true;

            // Update UI
            this.displayFacePhoto(photoDataUrl, userType);
            this.stopCamera();
            this.hideFaceCameraInterface(userType);
            this.showFacePhotoPreview(userType);

            // Update step indicators
            this.updateStepIndicators(userType);

            // Dispatch event for form validation update
            document.dispatchEvent(new CustomEvent('photoCaptured', {
                detail: { type: 'face', userType: userType }
            }));

            console.log(`Face photo captured successfully for ${userType} user`);

        } catch (error) {
            console.error('Error capturing face photo:', error);
            this.showError('Failed to capture photo. Please try again.');
        }
    }

    displayIdPhoto(photoDataUrl, userType = 'new') {
        // Update ID preview based on user type
        const previewElement = userType === 'new' ? this.elements.idPreviewImage : this.elements.existingIdPreviewImage;
        previewElement.innerHTML = `<img src="${photoDataUrl}" alt="ID Document">`;

        // Update ID card preview (shared between both user types)
        this.elements.idCardPreviewPhoto.innerHTML = `<img src="${photoDataUrl}" alt="ID Photo" style="width: 100%; height: 100%; object-fit: cover;">`;
    }

    displayFacePhoto(photoDataUrl, userType = 'new') {
        // Update face preview based on user type
        const previewElement = userType === 'new' ? this.elements.facePreviewImage : this.elements.existingFacePreviewImage;
        previewElement.innerHTML = `<img src="${photoDataUrl}" alt="Face Photo">`;
    }

    stopCamera() {
        if (this.currentStream) {
            this.currentStream.getTracks().forEach(track => track.stop());
            this.currentStream = null;
            console.log('Camera stopped');
        }
    }

    cancelCapture(userType = 'new') {
        console.log(`Canceling capture for ${userType} user...`);
        this.stopCamera();
        this.hideIdCameraInterface(userType);
        this.hideFaceCameraInterface(userType);
        this.activeCapture = null;
        this.activeUserType = null;
    }

    hideIdCameraInterface(userType = 'new') {
        if (userType === 'new') {
            this.elements.idCameraInterface.style.display = 'none';
        } else {
            this.elements.existingIdCameraInterface.style.display = 'none';
        }
    }

    hideFaceCameraInterface(userType = 'new') {
        if (userType === 'new') {
            this.elements.faceCameraInterface.style.display = 'none';
        } else {
            this.elements.existingFaceCameraInterface.style.display = 'none';
        }
    }

    showIdPhotoPreview(userType = 'new') {
        if (userType === 'new') {
            this.elements.idPhotoPreview.style.display = 'block';
        } else {
            this.elements.existingIdPhotoPreview.style.display = 'block';
        }
    }

    showFacePhotoPreview(userType = 'new') {
        if (userType === 'new') {
            this.elements.facePhotoPreview.style.display = 'block';
        } else {
            this.elements.existingFacePhotoPreview.style.display = 'block';
        }
    }

    retakeIdPhoto(userType = 'new') {
        console.log(`Retaking ID photo for ${userType} user...`);

        // Clear photo data and reset capture status
        const photoDataInput = userType === 'new' ? this.elements.idPhotoData : this.elements.existingIdPhotoData;
        const photoPreview = userType === 'new' ? this.elements.idPhotoPreview : this.elements.existingIdPhotoPreview;

        photoDataInput.value = '';
        photoPreview.style.display = 'none';
        this.captureStatus[userType].idCaptured = false;

        // Reset ID card preview
        this.elements.idCardPreviewPhoto.innerHTML = '<i class="fas fa-user photo-icon"></i><p class="photo-placeholder">Photo</p>';

        // If face was captured, also reset it since ID is prerequisite
        if (this.captureStatus[userType].faceCaptured) {
            this.retakeFacePhoto(userType);
        }

        // Disable face capture until ID is recaptured
        this.disableFaceCapture(userType);

        // Update step indicators
        this.updateStepIndicators(userType);

        // Start ID capture again
        this.startIdCapture(userType);
    }

    retakeFacePhoto(userType = 'new') {
        console.log(`Retaking face photo for ${userType} user...`);

        // Clear photo data and reset capture status
        const photoDataInput = userType === 'new' ? this.elements.facePhotoData : this.elements.existingFacePhotoData;
        const photoPreview = userType === 'new' ? this.elements.facePhotoPreview : this.elements.existingFacePhotoPreview;

        photoDataInput.value = '';
        photoPreview.style.display = 'none';
        this.captureStatus[userType].faceCaptured = false;

        // Update step indicators
        this.updateStepIndicators(userType);

        // Start face capture again
        this.startFaceCapture(userType);
    }

    enableFaceCapture(userType = 'new') {
        const faceBtn = userType === 'new' ? this.elements.captureFaceBtn : this.elements.existingCaptureFaceBtn;
        if (faceBtn) {
            faceBtn.disabled = false;
            faceBtn.style.opacity = '1';
            faceBtn.style.cursor = 'pointer';
            console.log(`Face capture enabled for ${userType} user`);
        }
    }

    disableFaceCapture(userType = 'new') {
        const faceBtn = userType === 'new' ? this.elements.captureFaceBtn : this.elements.existingCaptureFaceBtn;
        if (faceBtn) {
            faceBtn.disabled = true;
            faceBtn.style.opacity = '0.5';
            faceBtn.style.cursor = 'not-allowed';
            console.log(`Face capture disabled for ${userType} user`);
        }
    }

    updateStepIndicators(userType = 'new') {
        const prefix = userType === 'new' ? '' : 'existing';

        // Get step labels
        const step1Label = document.querySelector(`#${prefix}IdCaptureSection label`);
        const step2Label = document.querySelector(`#${prefix}FaceCaptureSection label`);

        if (step1Label && step2Label) {
            // Update Step 1 indicator
            if (this.captureStatus[userType].idCaptured) {
                step1Label.classList.add('step-completed');
                step1Label.classList.remove('step-disabled');
            } else {
                step1Label.classList.remove('step-completed');
                step1Label.classList.remove('step-disabled');
            }

            // Update Step 2 indicator
            if (this.captureStatus[userType].faceCaptured) {
                step2Label.classList.add('step-completed');
                step2Label.classList.remove('step-disabled');
            } else if (this.captureStatus[userType].idCaptured) {
                step2Label.classList.remove('step-completed');
                step2Label.classList.remove('step-disabled');
            } else {
                step2Label.classList.remove('step-completed');
                step2Label.classList.add('step-disabled');
            }
        }
    }

    validateFormSubmission(event, userType = 'new') {
        console.log(`Validating form submission for ${userType} user...`);

        const idPhotoData = userType === 'new' ? this.elements.idPhotoData.value : this.elements.existingIdPhotoData.value;
        const facePhotoData = userType === 'new' ? this.elements.facePhotoData.value : this.elements.existingFacePhotoData.value;

        if (!idPhotoData) {
            event.preventDefault();
            this.showError('Please capture your ID document first.');
            return false;
        }

        if (!facePhotoData) {
            event.preventDefault();
            this.showError('Please capture your face photo first.');
            return false;
        }

        console.log(`Form validation passed - both photos captured for ${userType} user`);
        return true;
    }

    showError(message) {
        console.error('Error:', message);
        alert(message); // Simple error display
    }

    showSuccess(message) {
        console.log('Success:', message);
        alert(message); // Simple success display
    }
}

// Initialize the simple capture system when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, checking for capture elements...');

    // Only initialize if we're on the create ID page
    if (document.getElementById('captureIdBtn')) {
        console.log('Capture elements found, initializing Simple Capture System...');
        window.simpleCaptureSystem = new SimpleCaptureSystem();
    } else {
        console.log('Capture elements not found, skipping initialization');
    }
});
