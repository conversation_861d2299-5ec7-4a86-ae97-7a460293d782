/**
 * Simplified Two-Step Camera Capture System
 *
 * This module handles:
 * 1. ID Document capture with front-facing camera
 * 2. Face capture with front-facing camera
 * 3. Basic photo storage in base64 format
 *
 * No advanced facial recognition - just basic capture functionality
 */

class SimpleCaptureSystem {
    constructor() {
        this.currentStream = null;
        this.activeCapture = null; // 'id' or 'face'

        // DOM elements
        this.elements = {
            // ID Capture elements
            captureIdBtn: document.getElementById('captureIdBtn'),
            idCameraInterface: document.getElementById('idCameraInterface'),
            idCameraVideo: document.getElementById('idCameraVideo'),
            idCameraCanvas: document.getElementById('idCameraCanvas'),
            takeIdPhotoBtn: document.getElementById('takeIdPhotoBtn'),
            cancelIdCameraBtn: document.getElementById('cancelIdCameraBtn'),
            idPhotoPreview: document.getElementById('idPhotoPreview'),
            idPreviewImage: document.getElementById('idPreviewImage'),
            retakeIdBtn: document.getElementById('retakeIdBtn'),
            idPhotoData: document.getElementById('idPhotoData'),

            // Face Capture elements
            captureFaceBtn: document.getElementById('captureFaceBtn'),
            faceCameraInterface: document.getElementById('faceCameraInterface'),
            faceCameraVideo: document.getElementById('faceCameraVideo'),
            faceCameraCanvas: document.getElementById('faceCameraCanvas'),
            takeFacePhotoBtn: document.getElementById('takeFacePhotoBtn'),
            cancelFaceCameraBtn: document.getElementById('cancelFaceCameraBtn'),
            facePhotoPreview: document.getElementById('facePhotoPreview'),
            facePreviewImage: document.getElementById('facePreviewImage'),
            retakeFaceBtn: document.getElementById('retakeFaceBtn'),
            facePhotoData: document.getElementById('facePhotoData'),

            // ID Card Preview
            idCardPreviewPhoto: document.getElementById('idCardPreviewPhoto'),

            // Form
            idCardForm: document.getElementById('idCardForm')
        };

        this.init();
    }

    init() {
        console.log('Initializing Simple Capture System...');
        console.log('Available elements:', {
            captureIdBtn: !!this.elements.captureIdBtn,
            captureFaceBtn: !!this.elements.captureFaceBtn,
            idCameraInterface: !!this.elements.idCameraInterface,
            faceCameraInterface: !!this.elements.faceCameraInterface
        });
        this.setupEventListeners();
        console.log('Simple Capture System initialized successfully');
    }

    setupEventListeners() {
        // ID Capture event listeners
        this.elements.captureIdBtn?.addEventListener('click', () => {
            console.log('ID Capture button clicked');
            this.startIdCapture();
        });

        this.elements.takeIdPhotoBtn?.addEventListener('click', () => {
            console.log('Take ID Photo button clicked');
            this.captureIdPhoto();
        });

        this.elements.cancelIdCameraBtn?.addEventListener('click', () => {
            console.log('Cancel ID Camera button clicked');
            this.cancelCapture();
        });

        this.elements.retakeIdBtn?.addEventListener('click', () => {
            console.log('Retake ID button clicked');
            this.retakeIdPhoto();
        });

        // Face Capture event listeners
        this.elements.captureFaceBtn?.addEventListener('click', () => {
            console.log('Face Capture button clicked');
            this.startFaceCapture();
        });

        this.elements.takeFacePhotoBtn?.addEventListener('click', () => {
            console.log('Take Face Photo button clicked');
            this.captureFacePhoto();
        });

        this.elements.cancelFaceCameraBtn?.addEventListener('click', () => {
            console.log('Cancel Face Camera button clicked');
            this.cancelCapture();
        });

        this.elements.retakeFaceBtn?.addEventListener('click', () => {
            console.log('Retake Face button clicked');
            this.retakeFacePhoto();
        });

        // Form submission validation
        this.elements.idCardForm?.addEventListener('submit', (e) => {
            this.validateFormSubmission(e);
        });
    }

    async startIdCapture() {
        console.log('Starting ID capture...');
        this.activeCapture = 'id';

        try {
            await this.requestCamera();
            this.showCameraInterface('id');
        } catch (error) {
            console.error('Failed to start ID capture:', error);
            this.showError('Camera access denied. Please allow camera permissions and try again.');
        }
    }

    async startFaceCapture() {
        console.log('Starting face capture...');
        this.activeCapture = 'face';

        try {
            await this.requestCamera();
            this.showCameraInterface('face');
        } catch (error) {
            console.error('Failed to start face capture:', error);
            this.showError('Camera access denied. Please allow camera permissions and try again.');
        }
    }

    async requestCamera() {
        console.log('Requesting camera access...');

        // Standardized camera constraints - front-facing camera for both captures
        const constraints = {
            video: {
                width: { ideal: 1280 },
                height: { ideal: 720 },
                facingMode: 'user' // Front-facing camera for both ID and face capture
            }
        };

        try {
            this.currentStream = await navigator.mediaDevices.getUserMedia(constraints);
            console.log('Camera access granted successfully');

            // Set video source based on active capture
            if (this.activeCapture === 'id') {
                this.elements.idCameraVideo.srcObject = this.currentStream;
            } else if (this.activeCapture === 'face') {
                this.elements.faceCameraVideo.srcObject = this.currentStream;
            }

        } catch (error) {
            console.error('Camera access error:', error);
            console.error('Error name:', error.name);
            console.error('Error message:', error.message);

            let errorMessage = 'Camera access denied. ';
            if (error.name === 'NotAllowedError') {
                errorMessage += 'Please allow camera permissions and try again.';
            } else if (error.name === 'NotFoundError') {
                errorMessage += 'No camera found on this device.';
            } else if (error.name === 'NotSupportedError') {
                errorMessage += 'Camera not supported in this browser.';
            } else {
                errorMessage += 'Please check your camera settings and try again.';
            }

            throw new Error(errorMessage);
        }
    }

    showCameraInterface(type) {
        console.log(`Showing camera interface for ${type} capture`);

        if (type === 'id') {
            this.elements.idCameraInterface.style.display = 'block';
        } else if (type === 'face') {
            this.elements.faceCameraInterface.style.display = 'block';
        }
    }

    captureIdPhoto() {
        console.log('Capturing ID photo...');

        try {
            const canvas = this.elements.idCameraCanvas;
            const video = this.elements.idCameraVideo;

            // Set canvas dimensions to match video
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;

            // Draw video frame to canvas
            const ctx = canvas.getContext('2d');
            ctx.drawImage(video, 0, 0);

            // Convert to base64
            const photoDataUrl = canvas.toDataURL('image/jpeg', 0.8);

            // Store photo data
            this.elements.idPhotoData.value = photoDataUrl;

            // Update UI
            this.displayIdPhoto(photoDataUrl);
            this.stopCamera();
            this.hideIdCameraInterface();
            this.showIdPhotoPreview();

            console.log('ID photo captured successfully');

        } catch (error) {
            console.error('Error capturing ID photo:', error);
            this.showError('Failed to capture photo. Please try again.');
        }
    }

    captureFacePhoto() {
        console.log('Capturing face photo...');

        try {
            const canvas = this.elements.faceCameraCanvas;
            const video = this.elements.faceCameraVideo;

            // Set canvas dimensions to match video
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;

            // Draw video frame to canvas
            const ctx = canvas.getContext('2d');
            ctx.drawImage(video, 0, 0);

            // Convert to base64
            const photoDataUrl = canvas.toDataURL('image/jpeg', 0.8);

            // Store photo data
            this.elements.facePhotoData.value = photoDataUrl;

            // Update UI
            this.displayFacePhoto(photoDataUrl);
            this.stopCamera();
            this.hideFaceCameraInterface();
            this.showFacePhotoPreview();

            console.log('Face photo captured successfully');

        } catch (error) {
            console.error('Error capturing face photo:', error);
            this.showError('Failed to capture photo. Please try again.');
        }
    }

    displayIdPhoto(photoDataUrl) {
        // Update ID preview
        this.elements.idPreviewImage.innerHTML = `<img src="${photoDataUrl}" alt="ID Document">`;

        // Update ID card preview
        this.elements.idCardPreviewPhoto.innerHTML = `<img src="${photoDataUrl}" alt="ID Photo" style="width: 100%; height: 100%; object-fit: cover;">`;
    }

    displayFacePhoto(photoDataUrl) {
        // Update face preview
        this.elements.facePreviewImage.innerHTML = `<img src="${photoDataUrl}" alt="Face Photo">`;
    }

    stopCamera() {
        if (this.currentStream) {
            this.currentStream.getTracks().forEach(track => track.stop());
            this.currentStream = null;
            console.log('Camera stopped');
        }
    }

    cancelCapture() {
        console.log('Canceling capture...');
        this.stopCamera();
        this.hideIdCameraInterface();
        this.hideFaceCameraInterface();
        this.activeCapture = null;
    }

    hideIdCameraInterface() {
        this.elements.idCameraInterface.style.display = 'none';
    }

    hideFaceCameraInterface() {
        this.elements.faceCameraInterface.style.display = 'none';
    }

    showIdPhotoPreview() {
        this.elements.idPhotoPreview.style.display = 'block';
    }

    showFacePhotoPreview() {
        this.elements.facePhotoPreview.style.display = 'block';
    }

    retakeIdPhoto() {
        console.log('Retaking ID photo...');
        this.elements.idPhotoData.value = '';
        this.elements.idPhotoPreview.style.display = 'none';
        this.elements.idCardPreviewPhoto.innerHTML = '<i class="fas fa-user photo-icon"></i><p class="photo-placeholder">Photo</p>';
        this.startIdCapture();
    }

    retakeFacePhoto() {
        console.log('Retaking face photo...');
        this.elements.facePhotoData.value = '';
        this.elements.facePhotoPreview.style.display = 'none';
        this.startFaceCapture();
    }

    validateFormSubmission(event) {
        console.log('Validating form submission...');

        const idPhotoData = this.elements.idPhotoData.value;
        const facePhotoData = this.elements.facePhotoData.value;

        if (!idPhotoData) {
            event.preventDefault();
            this.showError('Please capture your ID document first.');
            return false;
        }

        if (!facePhotoData) {
            event.preventDefault();
            this.showError('Please capture your face photo first.');
            return false;
        }

        console.log('Form validation passed - both photos captured');
        return true;
    }

    showError(message) {
        console.error('Error:', message);
        alert(message); // Simple error display
    }

    showSuccess(message) {
        console.log('Success:', message);
        alert(message); // Simple success display
    }
}

// Initialize the simple capture system when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, checking for capture elements...');

    // Only initialize if we're on the create ID page
    if (document.getElementById('captureIdBtn')) {
        console.log('Capture elements found, initializing Simple Capture System...');
        window.simpleCaptureSystem = new SimpleCaptureSystem();
    } else {
        console.log('Capture elements not found, skipping initialization');
    }
});
