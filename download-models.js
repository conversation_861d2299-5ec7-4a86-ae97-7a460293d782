/**
 * Model Download Script for Face-API.js Production Implementation
 * 
 * This script downloads the actual Face-API.js models required for production
 * facial verification in the Student ID Application.
 * 
 * Usage: node download-models.js
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

// Model URLs from the official Face-API.js models repository
const MODEL_BASE_URL = 'https://raw.githubusercontent.com/justadudewhohacks/face-api.js-models/master';

const MODELS_TO_DOWNLOAD = [
    // SSD MobileNet V1 (Face Detection) - ~5.4MB
    {
        name: 'SSD MobileNet V1',
        files: [
            {
                url: `${MODEL_BASE_URL}/ssd_mobilenetv1_model-weights_manifest.json`,
                path: 'js/models/ssd_mobilenetv1/ssd_mobilenetv1_model-weights_manifest.json'
            },
            {
                url: `${MODEL_BASE_URL}/ssd_mobilenetv1_model-shard1.bin`,
                path: 'js/models/ssd_mobilenetv1/ssd_mobilenetv1_model-shard1.bin'
            }
        ]
    },
    
    // Face Landmark 68 (Facial Landmarks) - ~350KB
    {
        name: 'Face Landmark 68',
        files: [
            {
                url: `${MODEL_BASE_URL}/face_landmark_68_model-weights_manifest.json`,
                path: 'js/models/face_landmark_68/face_landmark_68_model-weights_manifest.json'
            },
            {
                url: `${MODEL_BASE_URL}/face_landmark_68_model-shard1.bin`,
                path: 'js/models/face_landmark_68/face_landmark_68_model-shard1.bin'
            }
        ]
    },
    
    // Face Recognition (Descriptors) - ~6.2MB
    {
        name: 'Face Recognition',
        files: [
            {
                url: `${MODEL_BASE_URL}/face_recognition_model-weights_manifest.json`,
                path: 'js/models/face_recognition/face_recognition_model-weights_manifest.json'
            },
            {
                url: `${MODEL_BASE_URL}/face_recognition_model-shard1.bin`,
                path: 'js/models/face_recognition/face_recognition_model-shard1.bin'
            },
            {
                url: `${MODEL_BASE_URL}/face_recognition_model-shard2.bin`,
                path: 'js/models/face_recognition/face_recognition_model-shard2.bin'
            }
        ]
    }
];

/**
 * Download a file from URL to local path
 */
function downloadFile(url, filePath) {
    return new Promise((resolve, reject) => {
        // Create directory if it doesn't exist
        const dir = path.dirname(filePath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }

        const file = fs.createWriteStream(filePath);
        
        console.log(`📥 Downloading: ${path.basename(filePath)}`);
        
        https.get(url, (response) => {
            if (response.statusCode !== 200) {
                reject(new Error(`Failed to download ${url}: ${response.statusCode}`));
                return;
            }

            const totalSize = parseInt(response.headers['content-length'], 10);
            let downloadedSize = 0;

            response.on('data', (chunk) => {
                downloadedSize += chunk.length;
                if (totalSize) {
                    const progress = ((downloadedSize / totalSize) * 100).toFixed(1);
                    process.stdout.write(`\r   Progress: ${progress}% (${(downloadedSize / 1024 / 1024).toFixed(2)}MB)`);
                }
            });

            response.pipe(file);

            file.on('finish', () => {
                file.close();
                console.log(`\n✅ Downloaded: ${path.basename(filePath)}`);
                resolve();
            });

        }).on('error', (error) => {
            fs.unlink(filePath, () => {}); // Delete partial file
            reject(error);
        });
    });
}

/**
 * Download all models
 */
async function downloadAllModels() {
    console.log('🚀 Starting Face-API.js model download for production implementation...\n');
    
    let totalFiles = 0;
    let downloadedFiles = 0;
    
    // Count total files
    MODELS_TO_DOWNLOAD.forEach(model => {
        totalFiles += model.files.length;
    });
    
    console.log(`📊 Total files to download: ${totalFiles}\n`);

    try {
        for (const model of MODELS_TO_DOWNLOAD) {
            console.log(`🔄 Downloading ${model.name} model...`);
            
            for (const file of model.files) {
                await downloadFile(file.url, file.path);
                downloadedFiles++;
            }
            
            console.log(`✅ ${model.name} model downloaded successfully\n`);
        }

        console.log('🎉 All Face-API.js models downloaded successfully!');
        console.log(`📁 Models saved to: js/models/`);
        console.log(`📊 Downloaded ${downloadedFiles}/${totalFiles} files`);
        console.log('\n🔧 Next steps:');
        console.log('1. Download actual Face-API.js library from:');
        console.log('   https://raw.githubusercontent.com/justadudewhohacks/face-api.js/master/dist/face-api.min.js');
        console.log('2. Replace js/face-api-lib.js with the downloaded face-api.min.js');
        console.log('3. Test the facial verification system');
        console.log('\n✨ Your Student ID Application is now ready for production facial verification!');

    } catch (error) {
        console.error('❌ Error downloading models:', error.message);
        process.exit(1);
    }
}

/**
 * Verify existing model files
 */
function verifyExistingModels() {
    console.log('🔍 Checking for existing model files...\n');
    
    let existingFiles = 0;
    let totalFiles = 0;
    
    MODELS_TO_DOWNLOAD.forEach(model => {
        console.log(`📂 ${model.name}:`);
        
        model.files.forEach(file => {
            totalFiles++;
            if (fs.existsSync(file.path)) {
                const stats = fs.statSync(file.path);
                const sizeInMB = (stats.size / 1024 / 1024).toFixed(2);
                console.log(`   ✅ ${path.basename(file.path)} (${sizeInMB}MB)`);
                existingFiles++;
            } else {
                console.log(`   ❌ ${path.basename(file.path)} (missing)`);
            }
        });
        
        console.log('');
    });
    
    console.log(`📊 Found ${existingFiles}/${totalFiles} model files`);
    
    if (existingFiles === totalFiles) {
        console.log('✅ All model files are present!');
        return true;
    } else {
        console.log('⚠️  Some model files are missing and need to be downloaded.');
        return false;
    }
}

// Main execution
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.includes('--check') || args.includes('-c')) {
        verifyExistingModels();
    } else if (args.includes('--help') || args.includes('-h')) {
        console.log('Face-API.js Model Download Script');
        console.log('');
        console.log('Usage:');
        console.log('  node download-models.js          Download all models');
        console.log('  node download-models.js --check  Check existing models');
        console.log('  node download-models.js --help   Show this help');
    } else {
        // Check first, then download if needed
        if (!verifyExistingModels()) {
            console.log('\n🔄 Starting download...\n');
            downloadAllModels();
        }
    }
}

module.exports = {
    downloadAllModels,
    verifyExistingModels,
    MODELS_TO_DOWNLOAD
};
