<?php
// Test PHP file to check if P<PERSON> is working correctly

// Start session
session_start();

// Generate a CSRF token
if (!isset($_SESSION['test_csrf_token'])) {
    $_SESSION['test_csrf_token'] = bin2hex(random_bytes(32));
}

// Set a session variable
$_SESSION['test_message'] = 'P<PERSON> is working correctly!';

// Output information
echo "<h1>PHP Test Page</h1>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>Session ID: " . session_id() . "</p>";
echo "<p>CSRF Token: " . $_SESSION['test_csrf_token'] . "</p>";
echo "<p>Session Message: " . $_SESSION['test_message'] . "</p>";
echo "<p>Session Data: <pre>" . print_r($_SESSION, true) . "</pre></p>";

// Check if cookies are enabled
if (count($_COOKIE) > 0) {
    echo "<p>Cookies are enabled. Current cookies: <pre>" . print_r($_COOKIE, true) . "</pre></p>";
} else {
    echo "<p>Cookies are not enabled or no cookies are set.</p>";
}

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<p>Form was submitted with the following data: <pre>" . print_r($_POST, true) . "</pre></p>";
}

// Display a test form
echo "<h2>Test Form</h2>";
echo "<form method='post' action='test.php'>";
echo "<input type='hidden' name='csrf_token' value='" . $_SESSION['test_csrf_token'] . "'>";
echo "<input type='text' name='test_input' placeholder='Enter some text'>";
echo "<button type='submit'>Submit</button>";
echo "</form>";

// Display links
echo "<h2>Test Links</h2>";
echo "<ul>";
echo "<li><a href='index.php'>Go to Index Page</a></li>";
echo "<li><a href='backend/login.php'>Go to Login Handler</a></li>";
echo "<li><a href='backend/get_messages.php'>Get Messages</a></li>";
echo "</ul>";
?>
