document.addEventListener('DOMContentLoaded', function() {
    // Photo upload preview
    const photoUpload = document.getElementById('photoUpload');
    const photoPreview = document.querySelector('.photo-preview');
    const idCardPhoto = document.querySelector('.id-card-photo');
    
    photoUpload.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                // Update the photo preview
                photoPreview.innerHTML = '';
                
                const img = document.createElement('img');
                img.src = e.target.result;
                img.style.width = '100%';
                img.style.height = '100%';
                img.style.objectFit = 'cover';
                img.style.borderRadius = '5px';
                
                photoPreview.appendChild(img);
                
                // Update the ID card preview photo
                idCardPhoto.innerHTML = '';
                
                const cardImg = document.createElement('img');
                cardImg.src = e.target.result;
                cardImg.style.width = '100%';
                cardImg.style.height = '100%';
                cardImg.style.objectFit = 'cover';
                
                idCardPhoto.appendChild(cardImg);
            }
            
            reader.readAsDataURL(file);
        }
    });
    
    // Live preview updates for ID card
    const fullNameInput = document.getElementById('fullName');
    const studentIdInput = document.getElementById('studentId');
    const departmentSelect = document.getElementById('department');
    const expectedGraduationInput = document.getElementById('expectedGraduation');
    
    const previewName = document.getElementById('previewName');
    const previewId = document.getElementById('previewId');
    const previewDepartment = document.getElementById('previewDepartment');
    const previewExpiry = document.getElementById('previewExpiry');
    
    // Update name on ID card preview
    fullNameInput.addEventListener('input', function() {
        previewName.textContent = this.value || 'John Doe';
    });
    
    // Update ID on ID card preview
    studentIdInput.addEventListener('input', function() {
        previewId.textContent = this.value || 'STUD1245';
    });
    
    // Update department on ID card preview
    departmentSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        previewDepartment.textContent = selectedOption.text || 'Engineering';
    });
    
    // Update expiry date on ID card preview
    expectedGraduationInput.addEventListener('input', function() {
        previewExpiry.textContent = this.value || '2025';
    });
    
    // Form submission
    const idCardForm = document.getElementById('idCardForm');
    
    idCardForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Validate form
        const fullName = fullNameInput.value.trim();
        const studentId = studentIdInput.value.trim();
        const department = departmentSelect.value;
        const termsAgree = document.getElementById('termsAgree').checked;
        
        if (!fullName) {
            alert('Please enter your full name');
            fullNameInput.focus();
            return;
        }
        
        if (!studentId) {
            alert('Please enter your student ID number');
            studentIdInput.focus();
            return;
        }
        
        if (!department) {
            alert('Please select your department');
            departmentSelect.focus();
            return;
        }
        
        if (!termsAgree) {
            alert('Please confirm that the information provided is accurate');
            return;
        }
        
        // Here you would typically send this data to a server
        // For now, just show an alert
        alert('ID Card application submitted successfully!');
        
        // Redirect to dashboard
        window.location.href = 'dashboard.html';
    });
});
