document.addEventListener('DOMContentLoaded', function() {
    // User type selection elements
    const newUserRadio = document.getElementById('newUser');
    const existingUserRadio = document.getElementById('existingUser');
    const newUserForm = document.getElementById('newUserForm');
    const existingUserForm = document.getElementById('existingUserForm');
    const selectedUserTypeInput = document.getElementById('selectedUserType');

    // User type selection functionality
    function toggleUserForms() {
        if (newUserRadio.checked) {
            newUserForm.style.display = 'block';
            existingUserForm.style.display = 'none';
            selectedUserTypeInput.value = 'new';
        } else if (existingUserRadio.checked) {
            newUserForm.style.display = 'none';
            existingUserForm.style.display = 'block';
            selectedUserTypeInput.value = 'existing';
        }
    }

    // Add event listeners for user type selection
    newUserRadio.addEventListener('change', toggleUserForms);
    existingUserRadio.addEventListener('change', toggleUserForms);

    // Initialize form display
    toggleUserForms();

    // Note: Photo capture is now handled by the facial recognition system
    // The facial recognition system will update the ID card preview when a photo is captured

    // Live preview updates for ID card
    const fullNameInput = document.getElementById('fullName');
    const studentIdInput = document.getElementById('studentId');
    const departmentSelect = document.getElementById('department');
    const issuedInput = document.getElementById('issued');

    const previewName = document.getElementById('previewName');
    const previewId = document.getElementById('previewId');
    const previewDepartment = document.getElementById('previewDepartment');
    const previewIssued = document.getElementById('previewIssued');

    // Update name on ID card preview
    fullNameInput.addEventListener('input', function() {
        previewName.textContent = this.value || 'John Doe';
    });

    // Update ID on ID card preview
    studentIdInput.addEventListener('input', function() {
        previewId.textContent = this.value || 'STUD1245';
    });

    // Update department on ID card preview
    departmentSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        previewDepartment.textContent = selectedOption.text || 'Faculty of Science and Engineering';
    });

    // Update issued date on ID card preview (read-only field, but we can listen for changes)
    issuedInput.addEventListener('input', function() {
        previewIssued.textContent = this.value || 'Pending Approval';
    });

    // Form submission
    const idCardForm = document.getElementById('idCardForm');

    idCardForm.addEventListener('submit', function(e) {
        // Check if user type is selected
        const userType = document.querySelector('input[name="user_type"]:checked');
        if (!userType) {
            e.preventDefault();
            alert('Please select a user type (New User or Existing User)');
            return;
        }

        // Only validate New User form if it's selected
        if (userType.value === 'new') {
            // Validate form
            const fullName = fullNameInput.value.trim();
            const studentId = studentIdInput.value.trim();
            const department = departmentSelect.value;
            const program = document.getElementById('program').value.trim();
            const emergencyContact = document.getElementById('emergencyContact').value.trim();
            const termsAgree = document.getElementById('termsAgree').checked;
            const idPhotoData = document.getElementById('idPhotoData').value;
            const facePhotoData = document.getElementById('facePhotoData').value;

            if (!fullName) {
                e.preventDefault();
                alert('Please enter your full name');
                fullNameInput.focus();
                return;
            }

            if (!studentId) {
                e.preventDefault();
                alert('Please enter your student ID number');
                studentIdInput.focus();
                return;
            }

            if (!department) {
                e.preventDefault();
                alert('Please select your faculty');
                departmentSelect.focus();
                return;
            }

            if (!program) {
                e.preventDefault();
                alert('Please enter your program or major');
                document.getElementById('program').focus();
                return;
            }

            if (!emergencyContact) {
                e.preventDefault();
                alert('Please enter your emergency contact');
                document.getElementById('emergencyContact').focus();
                return;
            }

            if (!idPhotoData) {
                e.preventDefault();
                alert('Please capture your ID document photo');
                return;
            }

            if (!facePhotoData) {
                e.preventDefault();
                alert('Please capture your face photo');
                return;
            }

            if (!termsAgree) {
                e.preventDefault();
                alert('Please confirm that the information provided is accurate');
                return;
            }
        } else if (userType.value === 'existing') {
            // Prevent submission for existing user (not implemented yet)
            e.preventDefault();
            alert('Existing User functionality is coming soon. Please select "New User" for now.');
            return;
        }

        // If all validation passes, the form will submit normally
        // Both photos are captured and stored in hidden inputs
    });
});
