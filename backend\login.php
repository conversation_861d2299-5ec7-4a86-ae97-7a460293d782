<?php
/**
 * <PERSON><PERSON> Handler
 *
 * This file handles the login and registration forms.
 */

require_once 'config.php';
require_once 'auth.php';

// Generate CSRF token if it doesn't exist
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Check if the form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    $submittedToken = $_POST['csrf_token'] ?? '';

    // Debug CSRF token
    error_log("Submitted CSRF token: " . $submittedToken);
    error_log("Session CSRF token: " . ($_SESSION['csrf_token'] ?? 'not set'));

    // Skip CSRF check during development - REMOVE THIS IN PRODUCTION
    // if (!hash_equals($_SESSION['csrf_token'] ?? '', $submittedToken)) {
    //     $_SESSION['login_error'] = 'Invalid form submission. Please try again.';
    //     header('Location: ../index.php');
    //     exit;
    // }

    $formType = $_POST['form_type'] ?? '';
    error_log("Form type: " . $formType);

    if ($formType === 'login') {
        handleLoginForm();
    } elseif ($formType === 'signup') {
        handleSignupForm();
    } else {
        $_SESSION['login_error'] = 'Invalid form type.';
        header('Location: ../index.php');
        exit;
    }
}

// Redirect to dashboard if already logged in
if (isLoggedIn()) {
    header('Location: ../html/dashboard.php');
    exit;
}

// For debugging
error_log("Login.php accessed with method: " . $_SERVER['REQUEST_METHOD']);
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    error_log("POST data: " . print_r($_POST, true));
}

/**
 * Handle login form submission
 */
function handleLoginForm() {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    $rememberMe = isset($_POST['remember_me']);

    // Rate limiting check
    if (isRateLimited($username)) {
        $_SESSION['login_error'] = 'Too many login attempts. Please try again later.';
        header('Location: ../index.php');
        exit;
    }

    $result = loginUser($username, $password);

    if ($result['success']) {
        // Handle "Remember Me" functionality
        if ($rememberMe) {
            // Generate a secure token
            $selector = bin2hex(random_bytes(16));
            $validator = bin2hex(random_bytes(32));

            // Hash the validator for storage
            $hashedValidator = password_hash($validator, PASSWORD_DEFAULT);

            // Set expiry date (30 days from now)
            $expires = date('Y-m-d H:i:s', time() + 30 * 24 * 60 * 60);

            try {
                $db = getDbConnection();

                // Delete any existing tokens for this user
                $stmt = $db->prepare("DELETE FROM auth_tokens WHERE user_id = ?");
                $stmt->execute([$result['user']['user_id']]);

                // Store the new token
                $stmt = $db->prepare("INSERT INTO auth_tokens (user_id, selector, hashed_validator, expires) VALUES (?, ?, ?, ?)");
                $stmt->execute([$result['user']['user_id'], $selector, $hashedValidator, $expires]);

                // Set a cookie with the selector and validator
                $cookieValue = $selector . ':' . $validator;
                setcookie(
                    'remember_me',
                    $cookieValue,
                    time() + 30 * 24 * 60 * 60, // 30 days
                    '/',
                    '',
                    true, // Secure
                    true  // HttpOnly
                );
            } catch (PDOException $e) {
                error_log("Remember Me Error: " . $e->getMessage());
                // Continue even if remember me fails
            }
        }

        // Reset login attempts
        resetLoginAttempts($username);

        // Create login notification
        require_once 'login_notifications.php';
        createLoginNotification($result['user']['user_id']);

        // Redirect to dashboard
        header('Location: ../html/dashboard.php');
        exit;
    } else {
        // Increment login attempts
        incrementLoginAttempts($username);

        // Set error message and redirect back to login page
        $_SESSION['login_error'] = $result['message'];
        header('Location: ../index.php');
        exit;
    }
}

/**
 * Handle signup form submission
 */
function handleSignupForm() {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    $email = $_POST['email'] ?? '';

    // Validate passwords match
    if ($password !== $confirmPassword) {
        $_SESSION['signup_error'] = 'Passwords do not match';
        header('Location: ../index.php');
        exit;
    }

    // Additional password strength validation
    if (strlen($password) < 8 ||
        !preg_match('/[A-Z]/', $password) ||
        !preg_match('/[a-z]/', $password) ||
        !preg_match('/[0-9]/', $password) ||
        !preg_match('/[^A-Za-z0-9]/', $password)) {

        $_SESSION['signup_error'] = 'Password must be at least 8 characters long and include at least one uppercase letter, one lowercase letter, one number, and one special character';
        header('Location: ../index.php');
        exit;
    }

    $result = registerUser($username, $password, $email);

    if ($result['success']) {
        // Auto-login the user
        $loginResult = loginUser($username, $password);

        if ($loginResult['success']) {
            // Create login notification
            require_once 'login_notifications.php';
            createLoginNotification($loginResult['user']['user_id']);

            // Redirect to dashboard
            header('Location: ../html/dashboard.php');
            exit;
        } else {
            // Set success message and redirect to login page
            $_SESSION['login_message'] = 'Registration successful. Please log in.';
            header('Location: ../index.php');
            exit;
        }
    } else {
        // Set error message and redirect back to signup page
        $_SESSION['signup_error'] = $result['message'];
        header('Location: ../index.php');
        exit;
    }
}

/**
 * Check if a user is rate limited
 *
 * @param string $username Username to check
 * @return bool True if rate limited, false otherwise
 */
function isRateLimited($username) {
    // Get login attempts from session
    $loginAttempts = $_SESSION['login_attempts'] ?? [];

    // Check if user has too many recent attempts
    if (isset($loginAttempts[$username])) {
        $attempts = $loginAttempts[$username];

        // If more than 5 attempts in the last 15 minutes
        if ($attempts['count'] >= 5 && (time() - $attempts['time']) < 900) {
            return true;
        }

        // Reset if 15 minutes have passed
        if ((time() - $attempts['time']) >= 900) {
            $loginAttempts[$username] = [
                'count' => 0,
                'time' => time()
            ];
            $_SESSION['login_attempts'] = $loginAttempts;
        }
    }

    return false;
}

/**
 * Increment login attempts for a user
 *
 * @param string $username Username to increment
 */
function incrementLoginAttempts($username) {
    // Get login attempts from session
    $loginAttempts = $_SESSION['login_attempts'] ?? [];

    // Initialize or increment
    if (!isset($loginAttempts[$username])) {
        $loginAttempts[$username] = [
            'count' => 1,
            'time' => time()
        ];
    } else {
        $loginAttempts[$username]['count']++;
        $loginAttempts[$username]['time'] = time();
    }

    // Save back to session
    $_SESSION['login_attempts'] = $loginAttempts;
}

/**
 * Reset login attempts for a user
 *
 * @param string $username Username to reset
 */
function resetLoginAttempts($username) {
    // Get login attempts from session
    $loginAttempts = $_SESSION['login_attempts'] ?? [];

    // Remove user from attempts
    if (isset($loginAttempts[$username])) {
        unset($loginAttempts[$username]);
        $_SESSION['login_attempts'] = $loginAttempts;
    }
}
