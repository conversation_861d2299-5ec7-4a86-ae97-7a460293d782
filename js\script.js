document.addEventListener('DOMContentLoaded', function() {
    const signupForm = document.getElementById('signup-form');
    const loginForm = document.getElementById('login-form');
    const showLoginBtn = document.getElementById('show-login');
    const showSignupBtn = document.getElementById('show-signup');
    const errorMessageDiv = document.getElementById('error-message');

    // Switch to login form
    showLoginBtn.addEventListener('click', function(e) {
        e.preventDefault();
        signupForm.classList.add('hidden');
        loginForm.classList.remove('hidden');
        clearErrorMessages();
    });

    // Switch to signup form
    showSignupBtn.addEventListener('click', function(e) {
        e.preventDefault();
        loginForm.classList.add('hidden');
        signupForm.classList.remove('hidden');
        clearErrorMessages();
    });

    // Check for error messages from PHP session
    fetch('backend/get_messages.php')
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                showError(data.error);
            }
            if (data.message) {
                showMessage(data.message);
            }
        })
        .catch(error => {
            console.error('Error fetching messages:', error);
        });

    // No additional handlers needed for terms links

    // Form validation and submission
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            // Get form type
            const formType = this.querySelector('input[name="form_type"]').value;

            if (formType === 'signup') {
                // Validate signup form
                const username = this.querySelector('input[name="username"]').value.trim();
                const email = this.querySelector('input[name="email"]').value.trim();
                const password = this.querySelector('input[name="password"]').value;
                const confirmPassword = this.querySelector('input[name="confirm_password"]').value;

                // Clear previous error messages
                clearErrorMessages();

                // Username validation (alphanumeric and underscore only)
                if (!isValidUsername(username)) {
                    e.preventDefault();
                    showError('Username can only contain letters, numbers, and underscores');
                    return false;
                }

                // Email validation
                if (!isValidEmail(email)) {
                    e.preventDefault();
                    showError('Please enter a valid email address');
                    return false;
                }

                // Password strength validation
                if (!isStrongPassword(password)) {
                    e.preventDefault();
                    showError('Password must be at least 8 characters long and include at least one uppercase letter, one lowercase letter, one number, and one special character');
                    return false;
                }

                // Password matching validation
                if (password !== confirmPassword) {
                    e.preventDefault();
                    showError('Passwords do not match');
                    return false;
                }

                // If we got here, validation passed - show loading indicator
                showLoading();
                console.log('Form validation passed, submitting...');
                return true;
            }
            else if (formType === 'login') {
                // Validate login form
                const username = this.querySelector('input[name="username"]').value.trim();
                const password = this.querySelector('input[name="password"]').value;

                // Clear previous error messages
                clearErrorMessages();

                // Basic validation
                if (!username || !password) {
                    e.preventDefault();
                    showError('Please enter both username and password');
                    return false;
                }

                // If we got here, validation passed - show loading indicator
                showLoading();
                console.log('Form validation passed, submitting...');
                return true;
            }
        });
    });

    // Helper functions
    function isValidUsername(username) {
        return /^[a-zA-Z0-9_]+$/.test(username);
    }

    function isValidEmail(email) {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    }

    function isStrongPassword(password) {
        // At least 8 characters, 1 uppercase, 1 lowercase, 1 number, 1 special character
        return password.length >= 8 &&
               /[A-Z]/.test(password) &&
               /[a-z]/.test(password) &&
               /[0-9]/.test(password) &&
               /[^A-Za-z0-9]/.test(password);
    }

    function showError(message) {
        if (errorMessageDiv) {
            errorMessageDiv.textContent = message;
            errorMessageDiv.style.display = 'block';
            errorMessageDiv.className = 'error-message';
        }
    }

    function showMessage(message) {
        if (errorMessageDiv) {
            errorMessageDiv.textContent = message;
            errorMessageDiv.style.display = 'block';
            errorMessageDiv.className = 'success-message';
        }
    }

    function clearErrorMessages() {
        if (errorMessageDiv) {
            errorMessageDiv.textContent = '';
            errorMessageDiv.style.display = 'none';
        }
    }

    function showLoading() {
        // Add loading indicator to the submit button
        const activeForm = document.querySelector('.form-container:not(.hidden)');
        const submitButton = activeForm.querySelector('button[type="submit"]');
        submitButton.innerHTML = '<span class="loading-spinner"></span> Processing...';
        submitButton.disabled = true;
    }
});