-- Add columns to id_applications table for document upload and enhanced functionality
-- This migration adds support for proof of registration documents and user type tracking

-- Add proof_of_registration column to store document path
ALTER TABLE id_applications 
ADD COLUMN proof_of_registration VARCHAR(255) NULL AFTER notes;

-- Add id_card_photo column to store captured ID photo path
ALTER TABLE id_applications 
ADD COLUMN id_card_photo VARCHAR(255) NULL AFTER proof_of_registration;

-- Add user_type column to distinguish between new and existing users
ALTER TABLE id_applications 
ADD COLUMN user_type ENUM('new', 'existing') DEFAULT 'new' AFTER id_card_photo;

-- Add face_photo column to store captured face photo path
ALTER TABLE id_applications 
ADD COLUMN face_photo VARCHAR(255) NULL AFTER user_type;

-- Add index for better performance on document queries
CREATE INDEX idx_applications_proof_document ON id_applications(proof_of_registration);
CREATE INDEX idx_applications_user_type ON id_applications(user_type);

-- Update existing records to have default user_type
UPDATE id_applications SET user_type = 'new' WHERE user_type IS NULL;
