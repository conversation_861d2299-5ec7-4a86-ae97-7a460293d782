* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Arial', sans-serif;
}

body {
    background-color: #f5f7fa;
}

.dashboard-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar Styles */
.sidebar {
    width: 250px;
    background-color: #5e259b;
    color: white;
    padding: 20px;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease;
}

.logo a {
    color: white;
    text-decoration: none;
    font-size: 20px;
    font-weight: bold;
}

.logo a:hover {
    text-decoration: none;
    opacity: 0.9;
}

.profile-image-box {
    width: 80px;
    height: 80px;
    background-color: #8a3df9;
    margin: 15px auto;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.profile-image-box img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.initials-placeholder {
    font-size: 28px;
    font-weight: bold;
    color: white;
}

.user-info-container {
    text-align: center;
    margin-bottom: 20px;
}

.user-name {
    font-weight: bold;
    margin-bottom: 5px;
    font-size: 16px;
}

.user-id {
    font-size: 12px;
    opacity: 0.8;
}

/* Sidebar Navigation Styles */
.sidebar-nav {
    margin-top: 10px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    margin-bottom: 5px;
    transition: all 0.3s;
}

.nav-item i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.nav-item.active {
    background-color: rgba(255, 255, 255, 0.2);
}

.nav-item:hover:not(.active) {
    background-color: rgba(255, 255, 255, 0.1);
}

.logout-btn {
    margin-top: auto;
}

/* Main Content Styles */
.main-content {
    flex: 1;
    background-color: #f5f7fa;
    margin-left: 250px;
    transition: margin-left 0.3s ease;
}

.top-bar {
    display: flex;
    align-items: center;
    padding: 15px 30px;
    background-color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.menu-toggle {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    margin-right: 20px;
    color: #666;
}

.search-bar {
    flex: 1;
    position: relative;
    max-width: 400px;
}

.search-bar input {
    width: 100%;
    padding: 10px 15px;
    padding-right: 40px;
    border: 1px solid #ddd;
    border-radius: 20px;
    font-size: 14px;
}

.search-bar i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

.top-icons {
    display: flex;
    margin-left: auto;
}

.icon-badge {
    position: relative;
    margin-left: 20px;
    font-size: 20px;
    color: #666;
    cursor: pointer;
}

.badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: #ff5252;
    color: white;
    font-size: 10px;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Dashboard Content Styles */
.dashboard-content {
    padding: 30px;
}

.welcome-section {
    margin-bottom: 30px;
}

.welcome-section h1 {
    font-size: 24px;
    margin-bottom: 5px;
}

.welcome-section p {
    color: #666;
}

.application-status {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.status-icon {
    width: 50px;
    height: 50px;
    background-color: #fff9e6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    color: #ffc107;
    font-size: 20px;
}

.status-details h3 {
    margin-bottom: 5px;
}

.status {
    margin-bottom: 5px;
}

.pending {
    color: #ffc107;
    font-weight: bold;
}

.date {
    font-size: 14px;
    color: #888;
}

.notifications-section, .quick-actions {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.view-all {
    color: #5e259b;
    text-decoration: none;
    font-size: 14px;
}

.notification-item {
    display: flex;
    padding: 15px 0;
    border-top: 1px solid #eee;
}

.notification-icon {
    width: 40px;
    height: 40px;
    background-color: #f0e6ff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: #5e259b;
}

.notification-content h3 {
    font-size: 16px;
    margin-bottom: 5px;
}

.notification-time {
    font-size: 12px;
    color: #888;
    margin-top: 5px;
}

.quick-actions h2 {
    margin-bottom: 20px;
}

.action-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border: 1px solid #eee;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
}

.action-item:hover {
    background-color: #f9f9f9;
}

.action-icon {
    width: 40px;
    height: 40px;
    background-color: #f0e6ff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: #5e259b;
}

/* Footer Styles */
.site-footer {
    background-color: #f5f7fa;
    border-top: 1px solid #e0e0e0;
    padding: 20px 30px;
    margin-top: 30px;
}

.footer-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.footer-links {
    margin-bottom: 10px;
}

.footer-links a {
    color: #5e259b;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s;
}

.footer-links a:hover {
    color: #8a3df9;
    text-decoration: underline;
}

.footer-divider {
    margin: 0 10px;
    color: #ccc;
}

.footer-copyright {
    font-size: 12px;
    color: #888;
}

/* Terms of Service Page Styles */
.terms-container {
    background-color: white;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
}

.terms-container h1 {
    font-size: 24px;
    margin-bottom: 20px;
    color: #333;
}

.terms-container h2 {
    font-size: 20px;
    margin-top: 30px;
    margin-bottom: 15px;
    color: #5e259b;
}

.terms-container h3 {
    font-size: 18px;
    margin-top: 25px;
    margin-bottom: 10px;
    color: #444;
}

.terms-container p {
    margin-bottom: 15px;
    line-height: 1.6;
    color: #555;
}

.terms-container ul {
    margin-bottom: 15px;
    padding-left: 20px;
}

.terms-container li {
    margin-bottom: 8px;
    line-height: 1.6;
}

.terms-container .last-updated {
    font-style: italic;
    color: #888;
    margin-bottom: 25px;
}

.return-button {
    display: inline-block;
    background-color: #5e259b;
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    text-decoration: none;
    margin-top: 20px;
    transition: background-color 0.3s;
}

.return-button:hover {
    background-color: #8a3df9;
}

.sidebar-hidden {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
}

@media (max-width: 900px) {
    .sidebar {
        position: fixed;
        left: 0;
        top: 0;
        height: 100vh;
        z-index: 1000;
        box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }
    .sidebar.sidebar-hidden {
        transform: translateX(-100%);
    }
    .sidebar {
        transform: translateX(0);
    }
}