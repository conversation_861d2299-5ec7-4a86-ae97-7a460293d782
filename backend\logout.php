<?php
/**
 * Logout Handler
 *
 * This file handles the logout process.
 */

require_once 'config.php';
require_once 'auth.php';

// Get user ID before logout for token deletion
$userId = $_SESSION['user_id'] ?? null;

// Logout the user
logoutUser();

// Clear remember me cookie
if (isset($_COOKIE['remember_me'])) {
    // Delete the cookie
    setcookie('remember_me', '', time() - 3600, '/', '', true, true);

    // Delete the token from the database if we have the user ID
    if ($userId) {
        try {
            $db = getDbConnection();
            $stmt = $db->prepare("DELETE FROM auth_tokens WHERE user_id = ?");
            $stmt->execute([$userId]);
        } catch (PDOException $e) {
            error_log("Logout Error: " . $e->getMessage());
        }
    }
}

// Redirect to login page
header('Location: ../index.php');
exit;
