<?php
/**
 * Face-API.js Model Server
 * Serves Face-API.js model files locally for improved performance
 */

// Set proper CORS headers for model loading
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Get the requested model file
$modelFile = $_GET['model'] ?? '';

// Define available models and their file mappings
$availableModels = [
    // TinyFaceDetector models
    'tiny_face_detector_model-weights_manifest.json' => 'tiny_face_detector/tiny_face_detector_model-weights_manifest.json',
    'tiny_face_detector_model-shard1' => 'tiny_face_detector/tiny_face_detector_model-shard1',
    
    // SSD MobileNetV1 models
    'ssd_mobilenetv1_model-weights_manifest.json' => 'ssd_mobilenetv1/ssd_mobilenetv1_model-weights_manifest.json',
    'ssd_mobilenetv1_model-shard1' => 'ssd_mobilenetv1/ssd_mobilenetv1_model-shard1',
    'ssd_mobilenetv1_model-shard2' => 'ssd_mobilenetv1/ssd_mobilenetv1_model-shard2',
    
    // Face Landmark 68 Point models
    'face_landmark_68_model-weights_manifest.json' => 'face_landmark_68/face_landmark_68_model-weights_manifest.json',
    'face_landmark_68_model-shard1' => 'face_landmark_68/face_landmark_68_model-shard1',
    
    // Face Recognition models
    'face_recognition_model-weights_manifest.json' => 'face_recognition/face_recognition_model-weights_manifest.json',
    'face_recognition_model-shard1' => 'face_recognition/face_recognition_model-shard1',
    'face_recognition_model-shard2' => 'face_recognition/face_recognition_model-shard2',
    
    // Face Expression models
    'face_expression_model-weights_manifest.json' => 'face_expression/face_expression_model-weights_manifest.json',
    'face_expression_model-shard1' => 'face_expression/face_expression_model-shard1',
    
    // Age Gender models
    'age_gender_model-weights_manifest.json' => 'age_gender/age_gender_model-weights_manifest.json',
    'age_gender_model-shard1' => 'age_gender/age_gender_model-shard1'
];

// Validate model request
if (empty($modelFile) || !isset($availableModels[$modelFile])) {
    http_response_code(404);
    echo json_encode(['error' => 'Model not found']);
    exit;
}

$filePath = __DIR__ . '/' . $availableModels[$modelFile];

// Check if file exists
if (!file_exists($filePath)) {
    http_response_code(404);
    echo json_encode(['error' => 'Model file not found on server']);
    exit;
}

// Set appropriate content type based on file extension
$fileExtension = pathinfo($filePath, PATHINFO_EXTENSION);
switch ($fileExtension) {
    case 'json':
        header('Content-Type: application/json');
        break;
    case 'bin':
    default:
        header('Content-Type: application/octet-stream');
        break;
}

// Set caching headers for better performance
header('Cache-Control: public, max-age=86400'); // Cache for 24 hours
header('ETag: "' . md5_file($filePath) . '"');
header('Last-Modified: ' . gmdate('D, d M Y H:i:s', filemtime($filePath)) . ' GMT');

// Check if client has cached version
$clientETag = $_SERVER['HTTP_IF_NONE_MATCH'] ?? '';
$serverETag = '"' . md5_file($filePath) . '"';

if ($clientETag === $serverETag) {
    http_response_code(304); // Not Modified
    exit;
}

// Serve the file
header('Content-Length: ' . filesize($filePath));
readfile($filePath);
?>
